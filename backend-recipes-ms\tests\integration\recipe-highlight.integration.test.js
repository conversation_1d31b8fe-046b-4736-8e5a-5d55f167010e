/**
 * Integration tests for recipe highlight feature
 */

const request = require("supertest");
const app = require("../../src/app");
const db = require("../../src/models");
const { RecipeHistoryAction } = require("../../src/models/RecipeHistory");

describe("Recipe Highlight Integration Tests", () => {
  let authToken;
  let testUser;
  let testOrganization;
  let testRecipe;

  beforeAll(async () => {
    // Setup test database
    await db.sequelize.sync({ force: true });

    // Create test user and organization
    testUser = await db.User.create({
      user_email: "<EMAIL>",
      user_first_name: "Test",
      user_last_name: "User",
      user_password: "hashedpassword",
      user_status: "active",
    });

    testOrganization = await db.Organization.create({
      organization_name: "Test Org",
      organization_status: "active",
    });

    // Create test recipe
    testRecipe = await db.Recipe.create({
      recipe_name: "Test Recipe",
      recipe_description: "A test recipe",
      recipe_status: "publish",
      organization_id: testOrganization.id,
      created_by: testUser.id,
      updated_by: testUser.id,
    });

    // Mock authentication token
    authToken = "Bearer mock-jwt-token";
  });

  afterAll(async () => {
    await db.sequelize.close();
  });

  beforeEach(async () => {
    // Clear recipe history before each test
    await db.RecipeHistory.destroy({ where: {} });
  });

  describe("GET /api/v1/private/recipe/:id", () => {
    it("should return recipe with highlight when recent changes exist", async () => {
      // Create a recent history entry
      await db.RecipeHistory.create({
        recipe_id: testRecipe.id,
        action: RecipeHistoryAction.ingredient_added,
        description: "Added new ingredient",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        field_name: "ingredient_name",
        created_at: new Date(),
      });

      const response = await request(app)
        .get(`/api/v1/private/recipe/${testRecipe.id}`)
        .set("Authorization", authToken)
        .expect(200);

      expect(response.body.status).toBe(true);
      expect(response.body.data).toHaveProperty("highlight");
      expect(response.body.data).toHaveProperty("hasRecentChanges");
      
      const highlight = response.body.data.highlight;
      expect(highlight).toMatchObject({
        component: "ingredients",
        action: RecipeHistoryAction.ingredient_added,
        description: "Added new ingredient",
        priority: 2,
        modifiedBy: {
          userId: testUser.id,
          userName: expect.any(String),
        },
      });
      
      expect(response.body.data.hasRecentChanges).toBe(true);
    });

    it("should return recipe without highlight when no recent changes", async () => {
      // Create an old history entry (older than 30 days)
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 35);

      await db.RecipeHistory.create({
        recipe_id: testRecipe.id,
        action: RecipeHistoryAction.created,
        description: "Recipe created",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        created_at: oldDate,
      });

      const response = await request(app)
        .get(`/api/v1/private/recipe/${testRecipe.id}`)
        .set("Authorization", authToken)
        .expect(200);

      expect(response.body.status).toBe(true);
      expect(response.body.data.highlight).toBeNull();
      expect(response.body.data.hasRecentChanges).toBe(false);
    });

    it("should exclude bookmark actions from highlights", async () => {
      // Create a bookmark history entry
      await db.RecipeHistory.create({
        recipe_id: testRecipe.id,
        action: RecipeHistoryAction.bookmark_added,
        description: "Recipe bookmarked",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        created_at: new Date(),
      });

      const response = await request(app)
        .get(`/api/v1/private/recipe/${testRecipe.id}`)
        .set("Authorization", authToken)
        .expect(200);

      expect(response.body.status).toBe(true);
      expect(response.body.data.highlight).toBeNull();
      expect(response.body.data.hasRecentChanges).toBe(false);
    });
  });

  describe("GET /api/v1/public/recipe/:id", () => {
    it("should return public recipe with highlight", async () => {
      // Create a recent history entry
      await db.RecipeHistory.create({
        recipe_id: testRecipe.id,
        action: RecipeHistoryAction.step_added,
        description: "Added cooking step",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        field_name: "step_description",
        created_at: new Date(),
      });

      const response = await request(app)
        .get(`/api/v1/public/recipe/${testRecipe.id}`)
        .expect(200);

      expect(response.body.status).toBe(true);
      expect(response.body.data).toHaveProperty("highlight");
      expect(response.body.data).toHaveProperty("hasRecentChanges");
      
      const highlight = response.body.data.highlight;
      expect(highlight).toMatchObject({
        component: "steps",
        action: RecipeHistoryAction.step_added,
        description: "Added cooking step",
        priority: 2,
      });
    });
  });

  describe("GET /api/v1/private/recipe/list", () => {
    let testRecipe2;

    beforeEach(async () => {
      // Create a second test recipe
      testRecipe2 = await db.Recipe.create({
        recipe_name: "Test Recipe 2",
        recipe_description: "Another test recipe",
        recipe_status: "publish",
        organization_id: testOrganization.id,
        created_by: testUser.id,
        updated_by: testUser.id,
      });
    });

    afterEach(async () => {
      if (testRecipe2) {
        await testRecipe2.destroy();
      }
    });

    it("should return recipe list with highlights", async () => {
      // Create history for both recipes
      await db.RecipeHistory.create({
        recipe_id: testRecipe.id,
        action: RecipeHistoryAction.ingredient_updated,
        description: "Updated ingredient",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        created_at: new Date(),
      });

      await db.RecipeHistory.create({
        recipe_id: testRecipe2.id,
        action: RecipeHistoryAction.category_added,
        description: "Added category",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        created_at: new Date(),
      });

      const response = await request(app)
        .get("/api/v1/private/recipe/list")
        .set("Authorization", authToken)
        .query({ page: 1, size: 10 })
        .expect(200);

      expect(response.body.status).toBe(true);
      expect(response.body.data).toHaveProperty("recipes");
      expect(response.body.data).toHaveProperty("highlight_metadata");

      const recipes = response.body.data.recipes;
      expect(recipes).toHaveLength(2);

      // Check that both recipes have highlight information
      const recipe1 = recipes.find(r => r.id === testRecipe.id);
      const recipe2 = recipes.find(r => r.id === testRecipe2.id);

      expect(recipe1).toHaveProperty("highlight");
      expect(recipe1).toHaveProperty("hasRecentChanges");
      expect(recipe1.highlight.component).toBe("ingredients");

      expect(recipe2).toHaveProperty("highlight");
      expect(recipe2).toHaveProperty("hasRecentChanges");
      expect(recipe2.highlight.component).toBe("categories");

      // Check highlight metadata
      const metadata = response.body.data.highlight_metadata;
      expect(metadata).toMatchObject({
        totalRecipes: 2,
        recipesWithHighlights: 2,
        queryTimestamp: expect.any(String),
      });
    });

    it("should handle recipes without recent changes", async () => {
      // Create old history for one recipe only
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 35);

      await db.RecipeHistory.create({
        recipe_id: testRecipe.id,
        action: RecipeHistoryAction.created,
        description: "Recipe created",
        created_by: testUser.id,
        organization_id: testOrganization.id,
        created_at: oldDate,
      });

      const response = await request(app)
        .get("/api/v1/private/recipe/list")
        .set("Authorization", authToken)
        .query({ page: 1, size: 10 })
        .expect(200);

      expect(response.body.status).toBe(true);
      
      const recipes = response.body.data.recipes;
      expect(recipes).toHaveLength(2);

      // Both recipes should have null highlights and false hasRecentChanges
      recipes.forEach(recipe => {
        expect(recipe.highlight).toBeNull();
        expect(recipe.hasRecentChanges).toBe(false);
      });

      // Check highlight metadata
      const metadata = response.body.data.highlight_metadata;
      expect(metadata).toMatchObject({
        totalRecipes: 2,
        recipesWithHighlights: 0,
        queryTimestamp: expect.any(String),
      });
    });
  });

  describe("Priority and Component Mapping", () => {
    it("should assign correct priorities to different actions", async () => {
      const testCases = [
        { action: RecipeHistoryAction.created, expectedPriority: 3 }, // HIGH
        { action: RecipeHistoryAction.archived, expectedPriority: 4 }, // CRITICAL
        { action: RecipeHistoryAction.ingredient_added, expectedPriority: 2 }, // MEDIUM
        { action: RecipeHistoryAction.category_added, expectedPriority: 1 }, // LOW
      ];

      for (const testCase of testCases) {
        // Clear previous history
        await db.RecipeHistory.destroy({ where: {} });

        // Create history entry
        await db.RecipeHistory.create({
          recipe_id: testRecipe.id,
          action: testCase.action,
          description: `Test ${testCase.action}`,
          created_by: testUser.id,
          organization_id: testOrganization.id,
          created_at: new Date(),
        });

        const response = await request(app)
          .get(`/api/v1/private/recipe/${testRecipe.id}`)
          .set("Authorization", authToken)
          .expect(200);

        expect(response.body.data.highlight.priority).toBe(testCase.expectedPriority);
      }
    });

    it("should map actions to correct components", async () => {
      const testCases = [
        { action: RecipeHistoryAction.ingredient_added, expectedComponent: "ingredients" },
        { action: RecipeHistoryAction.step_updated, expectedComponent: "steps" },
        { action: RecipeHistoryAction.category_added, expectedComponent: "categories" },
        { action: RecipeHistoryAction.published, expectedComponent: "status" },
      ];

      for (const testCase of testCases) {
        // Clear previous history
        await db.RecipeHistory.destroy({ where: {} });

        // Create history entry
        await db.RecipeHistory.create({
          recipe_id: testRecipe.id,
          action: testCase.action,
          description: `Test ${testCase.action}`,
          created_by: testUser.id,
          organization_id: testOrganization.id,
          created_at: new Date(),
        });

        const response = await request(app)
          .get(`/api/v1/private/recipe/${testRecipe.id}`)
          .set("Authorization", authToken)
          .expect(200);

        expect(response.body.data.highlight.component).toBe(testCase.expectedComponent);
      }
    });
  });
});
