import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { sequelize, db } from "../models";
import { AttributeStatus } from "../models/FoodAttributes";
import { generateUniqueSlug } from "../helper/slugGenerator";
import {
  isDefaultAccess,
  getPaginatedItems,
  getUserFullName,
} from "../helper/common";

// Get models from db object to ensure associations are set up
const FoodAttributes = db.FoodAttributes;
const Item = db.Item;
const RecipeAttributes = db.RecipeAttributes;
const IngredientAttributes = db.IngredientAttributes;
const RecipeIngredients = db.RecipeIngredients;

// Helper function to get the proper base URL
const getBaseUrl = (): string => {
  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    // API_BASE_URL already contains the full endpoint, return base part
    return baseUrl.replace(
      "/backend-api/v1/public/user/get-file?location=",
      ""
    );
  } else {
    // For development or when API_BASE_URL is just the base domain
    return (
      process.env.BASE_URL ||
      process.env.FRONTEND_URL ||
      "https://staging.namastevillage.theeasyaccess.com"
    );
  }
};

/**
 * Create a new food attribute (type-wise creation)
 * @route POST /api/v1/private/food-attributes
 * @access Private (Authenticated users)
 */
const createFoodAttribute = async (req: any, res: Response): Promise<any> => {
  try {
    const {
      attribute_title,
      attribute_description,
      attribute_status,
      attribute_type,
    } = req.body;

    // Check if user has default access (can create system defaults)
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    const organizationIdForCreation = hasDefaultAccess
      ? null
      : req.user?.organization_id;

    // Check for duplicate attribute title (case-insensitive)
    // Check both in same organization AND in default/system records
    const existingAttributeByTitle = await FoodAttributes.findOne({
      where: {
        [Op.and]: [
          db.sequelize.where(
            db.sequelize.fn("LOWER", db.sequelize.col("attribute_title")),
            db.sequelize.fn("LOWER", attribute_title.trim())
          ),
          { attribute_type: attribute_type },
          {
            [Op.or]: [
              { organization_id: organizationIdForCreation }, // Same organization
              { organization_id: null }, // Default/system records
            ],
          },
        ],
      },
    });

    if (existingAttributeByTitle) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ATTRIBUTE_TITLE_ALREADY_EXISTS"),
      });
    }

    // Generate unique slug from attribute title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existing = await FoodAttributes.findOne({
        where: {
          attribute_slug: slug,
          organization_id: organizationIdForCreation,
          attribute_type: attribute_type,
        },
      });
      return !!existing;
    };

    const attributeSlug = await generateUniqueSlug(
      attribute_title,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Set system attribute flag based on default access
    const isSystemAttributeFlag = hasDefaultAccess;

    // Create attribute first
    const attribute = await FoodAttributes.create({
      attribute_title: attribute_title,
      attribute_slug: attributeSlug,
      attribute_description: attribute_description,
      attribute_status: attribute_status || AttributeStatus.active,
      organization_id: organizationIdForCreation,
      attribute_type: attribute_type,
      is_system_attribute: isSystemAttributeFlag,
      created_by: req.user?.id || null,
      updated_by: req.user?.id || null,
    });

    let iconItemId = null;

    // Handle file upload from S3 middleware (req.files format)
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };
      if (files.attributeIcon && files.attributeIcon.length > 0) {
        const uploadedFile = files.attributeIcon[0];
        iconItemId = uploadedFile.item_id;
      }
    }

    // Update attribute with icon item_id if uploaded
    if (iconItemId) {
      await attribute.update({ attribute_icon: iconItemId });
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("ATTRIBUTE_CREATED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - createFoodAttribute:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Get all food attributes by type with advanced filtering and search
 * @route GET /api/v1/private/food-attributes
 * @access Private (Authenticated users)
 *
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - search: Search in title and description
 * - status: Filter by attribute status (active, inactive)
 * - type: Filter by attribute type (nutrition, allergen, cuisine, dietary)
 * - isSystemAttribute: Filter by system attributes (true/false) - Admin only
 * - organizationId: Filter by organization ID - Admin only
 * - sort: Sort field (default: attribute_title)
 * - order: Sort order (ASC/DESC, default: ASC)
 */
const getAllFoodAttributesByType = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    const {
      page,
      limit,
      search = "",
      status,
      type,
      isSystemAttribute,
      organizationId,
      sort = "attribute_title",
      order = "ASC",
    } = req.query;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build base where clause with organization isolation
    let whereClause: any = {};
    const searchConditions: any[] = [];

    // Organization-based access control
    if (hasDefaultAccess) {
      // Admin users can see all records, but can filter by organization
      if (organizationId !== undefined) {
        if (organizationId === "null" || organizationId === "") {
          whereClause.organization_id = null; // System defaults
        } else {
          whereClause.organization_id = organizationId;
        }
      }
    } else {
      // Regular users see their org records + system defaults
      whereClause = {
        [Op.or]: [
          { organization_id: req.user?.organization_id },
          { organization_id: null }, // System defaults
          { is_system_attribute: true }, // System attributes
        ],
      };
    }

    // Filter by attribute type (nutrition, allergen, cuisine, dietary)
    if (type) {
      whereClause.attribute_type = type;
    }

    // Filter by status (active, inactive)
    if (status) {
      whereClause.attribute_status = status;
    }

    // Filter by system attribute flag - accessible by all users
    if (isSystemAttribute !== undefined) {
      if (isSystemAttribute === "true") {
        whereClause.is_system_attribute = true;
      } else if (isSystemAttribute === "false") {
        whereClause.is_system_attribute = false;
      }
      // If isSystemAttribute is neither "true" nor "false", ignore the filter
    }

    // Search functionality (searches in title and description)
    if (search) {
      searchConditions.push({
        [Op.or]: [
          { attribute_title: { [Op.like]: `%${search}%` } },
          { attribute_description: { [Op.like]: `%${search}%` } },
        ],
      });
    }

    // Combine search conditions with where clause
    if (searchConditions.length > 0) {
      if (Object.keys(whereClause).length > 0) {
        whereClause = {
          [Op.and]: [whereClause, ...searchConditions],
        };
      } else {
        whereClause = {
          [Op.and]: searchConditions,
        };
      }
    }

    // Handle pagination - if limit is not provided, show all records
    const pageNumber = page ? Number(page) : 1;
    const limitNumber = limit ? Number(limit) : null; // null means no limit
    const offset = limitNumber ? (pageNumber - 1) * limitNumber : 0;

    // Validate sort field to prevent SQL injection
    const allowedSortFields = [
      "attribute_title",
      "attribute_description",
      "attribute_status",
      "attribute_type",
      "is_system_attribute",
      "organization_id",
      "created_at",
      "updated_at",
      "created_by",
      "updated_by",
    ];

    const sortField = allowedSortFields.includes(sort as string)
      ? (sort as string)
      : "attribute_title";
    const sortOrder =
      (order as string).toUpperCase() === "DESC" ? "DESC" : "ASC";

    // Build query options - conditionally add limit and offset
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: Item,
          as: "iconItem",
          attributes: [
            "id",
            "item_location",
            // Add computed iconUrl field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
              "iconUrl",
            ],
            // Add computed hasIcon field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
              "hasIcon",
            ],
          ],
          required: false, // LEFT JOIN to include attributes without icons
        },
      ],
      order: [[sortField, sortOrder]],
      raw: false,
      nest: true,
    };

    // Only add limit and offset if limit is provided
    if (limitNumber) {
      queryOptions.limit = limitNumber;
      queryOptions.offset = offset;
    }

    // Fetch attributes with conditional pagination
    const { rows: attributes, count } =
      await FoodAttributes.findAndCountAll(queryOptions);

    // Add user names to the attributes
    const attributesWithUserNames = await Promise.all(
      attributes.map(async (attribute: any) => {
        const attributeData = attribute.toJSON ? attribute.toJSON() : attribute;

        // Add created_by and updated_by user names
        if (attributeData.created_by) {
          attributeData.created_by_name = await getUserFullName(
            attributeData.created_by
          );
        }
        if (attributeData.updated_by) {
          attributeData.updated_by_name = await getUserFullName(
            attributeData.updated_by
          );
        }

        return attributeData;
      })
    );

    // Calculate pagination info only if limit is provided
    let paginationInfo: any = {
      count: count,
      data: attributesWithUserNames,
    };

    if (limitNumber) {
      const { total_pages } = getPaginatedItems(
        limitNumber,
        pageNumber,
        count || 0
      );

      paginationInfo = {
        ...paginationInfo,
        page: pageNumber,
        limit: limitNumber,
        total_pages: total_pages,
      };
    } else {
      // When no limit is provided, show all records info
      paginationInfo = {
        ...paginationInfo,
        page: 1,
        limit: "all",
        total_pages: 1,
        showing_all_records: true,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      ...paginationInfo,
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - getAllFoodAttributesByType:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Get single food attribute by ID and type
 * @route GET /api/v1/private/food-attributes/:id
 * @access Private (Authenticated users)
 */
const getFoodAttributeById = async (req: any, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { type } = req.query;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only see their org records + system defaults
      whereClause = {
        id,
        [Op.or]: [
          { organization_id: req.user?.organization_id },
          { organization_id: null }, // System defaults
          { is_system_attribute: true }, // System attributes
        ],
      };
    }

    // Add type filter if provided
    if (type) {
      whereClause.attribute_type = type;
    }

    const attribute = await FoodAttributes.findOne({
      where: whereClause,
      include: [
        {
          model: Item,
          as: "iconItem",
          attributes: [
            "id",
            "item_name",
            "item_location",
            "item_mime_type",
            // Add computed iconUrl field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
              "iconUrl",
            ],
            // Add computed hasIcon field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
              "hasIcon",
            ],
          ],
          required: false, // LEFT JOIN to include attribute even without icon
        },
      ],
      raw: false,
      nest: true,
    });

    if (!attribute) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("ATTRIBUTE_NOT_FOUND"),
        data: {},
      });
    }

    // Add user names to the attribute
    const attributeData = attribute.toJSON ? attribute.toJSON() : attribute;

    // Add created_by and updated_by user names
    if (attributeData.created_by) {
      attributeData.created_by_name = await getUserFullName(
        attributeData.created_by
      );
    }
    if (attributeData.updated_by) {
      attributeData.updated_by_name = await getUserFullName(
        attributeData.updated_by
      );
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: attributeData,
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - getFoodAttributeById:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Update food attribute by ID
 * @route PUT /api/v1/private/food-attributes/:id
 * @access Private (Authenticated users)
 */
const updateFoodAttribute = async (req: any, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const {
      attribute_title,
      attribute_description,
      attribute_status,
      attribute_type,
      is_system_attribute,
    } = req.body;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only update their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can update any record
      whereClause = { id };
    }

    // Find the attribute
    const attribute = await FoodAttributes.findOne({
      where: whereClause,
    });

    if (!attribute) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ATTRIBUTE_NOT_FOUND"),
      });
    }

    // Prevent regular users from updating system default records
    if (attribute.organization_id === null && !hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
      });
    }

    if (
      attribute_title &&
      attribute_title.trim() !== attribute.attribute_title
    ) {
      // For system defaults, check globally; for org records, check within org
      const duplicateCheckOrgId =
        hasDefaultAccess && attribute.organization_id === null
          ? null
          : req.user?.organization_id;

      // Check both in same organization AND in default/system records
      const existingAttributeByTitle = await FoodAttributes.findOne({
        where: {
          [Op.and]: [
            db.sequelize.where(
              db.sequelize.fn("LOWER", db.sequelize.col("attribute_title")),
              db.sequelize.fn("LOWER", attribute_title.trim())
            ),
            { attribute_type: attribute_type || attribute.attribute_type },
            {
              [Op.or]: [
                { organization_id: req.user?.organization_id }, // Same organization
                { organization_id: null }, // Default/system records
              ],
            },
            { id: { [Op.ne]: id } }, // Exclude current attribute
          ],
        },
      });

      if (existingAttributeByTitle) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ATTRIBUTE_TITLE_ALREADY_EXISTS"),
        });
      }
    }

    // Generate new slug if attribute title is being updated
    let newSlug = attribute.attribute_slug;
    if (attribute_title && attribute_title !== attribute.attribute_title) {
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        // For system defaults, check globally; for org records, check within org
        const slugCheckOrgId =
          hasDefaultAccess && attribute.organization_id === null
            ? null
            : req.user?.organization_id;

        const existing = await FoodAttributes.findOne({
          where: {
            attribute_slug: slug,
            organization_id: slugCheckOrgId,
            attribute_type: attribute_type || attribute.attribute_type,
            id: { [Op.ne]: id },
          },
        });
        return !!existing;
      };

      newSlug = await generateUniqueSlug(attribute_title, checkSlugExists, {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      });
    }

    let newIconItemId = attribute.attribute_icon;
    let iconWasUpdated = false;

    // Handle file upload from S3 middleware (req.files format)
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };
      if (files.attributeIcon && files.attributeIcon.length > 0) {
        const uploadedFile = files.attributeIcon[0];
        newIconItemId = uploadedFile.item_id;
        iconWasUpdated = true;
      } else if (files.attributeIcon && files.attributeIcon.length === 0) {
        // Empty file array means user wants to remove the icon
        newIconItemId = null;
        iconWasUpdated = true;
      }
    } else if (
      req.body.attributeIcon === "" ||
      req.body.attributeIcon === null
    ) {
      // Handle explicit removal of icon via form data
      newIconItemId = null;
      iconWasUpdated = true;
    }

    // Update the attribute
    await FoodAttributes.update(
      {
        attribute_title: attribute_title || attribute.attribute_title,
        attribute_slug: newSlug,
        attribute_description:
          attribute_description !== undefined
            ? attribute_description
            : attribute.attribute_description,
        attribute_icon: newIconItemId,
        attribute_status: attribute_status || attribute.attribute_status,
        attribute_type: attribute_type || attribute.attribute_type,
        is_system_attribute:
          is_system_attribute !== undefined
            ? is_system_attribute
            : attribute.is_system_attribute,
        updated_by: req.user?.id || null,
      },
      {
        where: whereClause,
      }
    );

    // Fetch updated attribute using the same where clause
    const updatedAttribute = await FoodAttributes.findOne({
      where: whereClause,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("ATTRIBUTE_UPDATED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - updateFoodAttribute:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Delete food attribute by ID (hard delete if not in use, otherwise show usage message)
 * @route DELETE /api/v1/private/food-attributes/:id
 * @access Private (Authenticated users)
 */
const deleteFoodAttribute = async (req: any, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only delete their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can delete any record
      whereClause = { id };
    }

    const attribute = await FoodAttributes.findOne({
      where: whereClause,
      transaction,
    });

    if (!attribute) {
      await transaction.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ATTRIBUTE_NOT_FOUND"),
      });
    }

    // Prevent deletion of system default records (organization_id: null) by non-default users
    if (attribute.organization_id === null && !hasDefaultAccess) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_DELETE_SYSTEM_DEFAULT"),
      });
    }

    // Prevent deletion of system attributes by non-default users
    if (attribute.is_system_attribute && !hasDefaultAccess) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_DELETE_SYSTEM_ATTRIBUTE"),
      });
    }

    // Check if attribute is being used in recipes (any status)
    const recipeUsage = await RecipeAttributes.count({
      where: {
        attributes_id: id,
      },
      transaction,
    });

    // Check if attribute is being used in ingredients (any status)
    const ingredientUsage = await IngredientAttributes.count({
      where: {
        attributes_id: id,
      },
      transaction,
    });

    // Check if attribute is being used in recipe ingredients as cooking method
    const recipeIngredientCookingUsage = await RecipeIngredients.count({
      where: {
        ingredient_cooking_method: id,
      },
      transaction,
    });

    // Check if attribute is being used in recipe ingredients as preparation method
    const recipeIngredientPrepUsage = await RecipeIngredients.count({
      where: {
        preparation_method: id,
      },
      transaction,
    });

    const totalUsage =
      recipeUsage +
      ingredientUsage +
      recipeIngredientCookingUsage +
      recipeIngredientPrepUsage;

    // If attribute is in use, prevent deletion
    if (totalUsage > 0) {
      await transaction.rollback();

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ATTRIBUTE_IN_USE_CANNOT_DELETE"),
      });
    }

    // Hard delete the attribute and all its inactive relations
    await IngredientAttributes.destroy({
      where: { attributes_id: id },
      transaction,
    });

    await RecipeAttributes.destroy({
      where: { attributes_id: id },
      transaction,
    });

    await FoodAttributes.destroy({
      where: whereClause,
      transaction,
    });

    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("ATTRIBUTE_DELETED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - deleteFoodAttribute:",
      error
    );
    await transaction.rollback();
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Deactivate food attribute by ID
 * @route PUT /api/v1/private/food-attributes/:id/deactivate
 * @access Private (Authenticated users)
 */
const deactivateFoodAttribute = async (
  req: any,
  res: Response
): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only deactivate their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can deactivate any record
      whereClause = { id };
    }

    const attribute = await FoodAttributes.findOne({
      where: whereClause,
      transaction,
    });

    if (!attribute) {
      await transaction.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ATTRIBUTE_NOT_FOUND"),
      });
    }

    // Prevent deactivation of system default records by non-default users
    if (attribute.organization_id === null && !hasDefaultAccess) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
      });
    }

    // Deactivate the attribute
    await FoodAttributes.update(
      {
        attribute_status: AttributeStatus.inactive,
        updated_by: req.user?.id || null,
      },
      {
        where: whereClause,
        transaction,
      }
    );

    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("ATTRIBUTE_DEACTIVATED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - deactivateFoodAttribute:",
      error
    );
    await transaction.rollback();
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Reactivate food attribute by ID
 * @route PUT /api/v1/private/food-attributes/:id/reactivate
 * @access Private (Authenticated users)
 */
const reactivateFoodAttribute = async (
  req: any,
  res: Response
): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only reactivate their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can reactivate any record
      whereClause = { id };
    }

    const attribute = await FoodAttributes.findOne({
      where: whereClause,
      transaction,
    });

    if (!attribute) {
      await transaction.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("ATTRIBUTE_NOT_FOUND"),
      });
    }

    // Prevent reactivation of system default records by non-default users
    if (attribute.organization_id === null && !hasDefaultAccess) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
      });
    }

    // Reactivate the attribute
    await FoodAttributes.update(
      {
        attribute_status: AttributeStatus.active,
        updated_by: req.user?.id || null,
      },
      {
        where: whereClause,
        transaction,
      }
    );

    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("ATTRIBUTE_REACTIVATED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in foodAttributes.controller.ts - reactivateFoodAttribute:",
      error
    );
    await transaction.rollback();
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

// Default export object
export default {
  createFoodAttribute,
  getAllFoodAttributesByType,
  getFoodAttributeById,
  updateFoodAttribute,
  deleteFoodAttribute,
  deactivateFoodAttribute,
  reactivateFoodAttribute,
};
