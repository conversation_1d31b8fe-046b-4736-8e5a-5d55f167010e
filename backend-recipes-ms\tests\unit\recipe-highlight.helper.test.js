/**
 * Unit tests for recipe highlight helper functions
 */

const { Op, QueryTypes } = require("sequelize");
const { 
  getRecipeHighlight, 
  hasRecentChanges, 
  getBulkRecipeHighlights 
} = require("../../src/helper/recipe-highlight.helper");
const { RecipeHistory, RecipeHistoryAction } = require("../../src/models/RecipeHistory");
const { getUser } = require("../../src/helper/common");
const { sequelize } = require("../../src/models/index");

// Mock dependencies
jest.mock("../../src/models/RecipeHistory");
jest.mock("../../src/helper/common");
jest.mock("../../src/models/index");

describe("Recipe Highlight Helper", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getRecipeHighlight", () => {
    it("should return null when no history found", async () => {
      RecipeHistory.findOne.mockResolvedValue(null);

      const result = await getRecipeHighlight(1, "org123");

      expect(result).toBeNull();
      expect(RecipeHistory.findOne).toHaveBeenCalledWith({
        where: expect.objectContaining({
          recipe_id: 1,
          created_at: expect.any(Object),
        }),
        order: [["created_at", "DESC"]],
      });
    });

    it("should return highlight for ingredient addition", async () => {
      const mockHistory = {
        id: 1,
        recipe_id: 1,
        action: RecipeHistoryAction.ingredient_added,
        description: "Added chicken breast",
        created_at: new Date("2024-01-15T10:30:00Z"),
        created_by: 123,
        field_name: "ingredient_name",
      };

      const mockUser = {
        user_full_name: "John Doe",
        user_email: "<EMAIL>",
      };

      RecipeHistory.findOne.mockResolvedValue(mockHistory);
      getUser.mockResolvedValue(mockUser);

      const result = await getRecipeHighlight(1, "org123");

      expect(result).toEqual({
        component: "ingredients",
        action: RecipeHistoryAction.ingredient_added,
        description: "Added chicken breast",
        lastModified: mockHistory.created_at,
        modifiedBy: {
          userId: 123,
          userName: "John Doe",
        },
        priority: 2, // MEDIUM priority
        context: {
          fieldName: "ingredient_name",
        },
      });
    });

    it("should generate default description when none provided", async () => {
      const mockHistory = {
        id: 1,
        recipe_id: 1,
        action: RecipeHistoryAction.step_added,
        description: null,
        created_at: new Date("2024-01-15T10:30:00Z"),
        created_by: 123,
        field_name: null,
      };

      const mockUser = {
        user_first_name: "Jane",
        user_email: "<EMAIL>",
      };

      RecipeHistory.findOne.mockResolvedValue(mockHistory);
      getUser.mockResolvedValue(mockUser);

      const result = await getRecipeHighlight(1, "org123");

      expect(result.description).toBe("New step was added");
      expect(result.modifiedBy.userName).toBe("Jane");
    });

    it("should handle user with only email", async () => {
      const mockHistory = {
        id: 1,
        recipe_id: 1,
        action: RecipeHistoryAction.updated,
        description: "Recipe updated",
        created_at: new Date("2024-01-15T10:30:00Z"),
        created_by: 123,
        field_name: null,
      };

      const mockUser = {
        user_email: "<EMAIL>",
      };

      RecipeHistory.findOne.mockResolvedValue(mockHistory);
      getUser.mockResolvedValue(mockUser);

      const result = await getRecipeHighlight(1, "org123");

      expect(result.modifiedBy.userName).toBe("<EMAIL>");
    });

    it("should handle missing user info", async () => {
      const mockHistory = {
        id: 1,
        recipe_id: 1,
        action: RecipeHistoryAction.created,
        description: "Recipe created",
        created_at: new Date("2024-01-15T10:30:00Z"),
        created_by: 123,
        field_name: null,
      };

      RecipeHistory.findOne.mockResolvedValue(mockHistory);
      getUser.mockResolvedValue(null);

      const result = await getRecipeHighlight(1, "org123");

      expect(result.modifiedBy.userName).toBe("Unknown User");
    });

    it("should exclude bookmark actions by default", async () => {
      const mockHistory = {
        id: 1,
        recipe_id: 1,
        action: RecipeHistoryAction.bookmark_added,
        description: "Bookmark added",
        created_at: new Date("2024-01-15T10:30:00Z"),
        created_by: 123,
        field_name: null,
      };

      RecipeHistory.findOne.mockResolvedValue(mockHistory);

      const result = await getRecipeHighlight(1, "org123");

      expect(RecipeHistory.findOne).toHaveBeenCalledWith({
        where: expect.objectContaining({
          action: {
            [Op.notIn]: [
              RecipeHistoryAction.bookmark_added,
              RecipeHistoryAction.bookmark_removed,
            ],
          },
        }),
        order: [["created_at", "DESC"]],
      });
    });
  });

  describe("hasRecentChanges", () => {
    it("should return true when recent changes exist", async () => {
      RecipeHistory.count.mockResolvedValue(3);

      const result = await hasRecentChanges(1, "org123", 7);

      expect(result).toBe(true);
      expect(RecipeHistory.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          recipe_id: 1,
          created_at: expect.any(Object),
        }),
      });
    });

    it("should return false when no recent changes", async () => {
      RecipeHistory.count.mockResolvedValue(0);

      const result = await hasRecentChanges(1, "org123", 7);

      expect(result).toBe(false);
    });

    it("should use default 7 days when not specified", async () => {
      RecipeHistory.count.mockResolvedValue(1);

      await hasRecentChanges(1, "org123");

      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() - 7);

      expect(RecipeHistory.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          recipe_id: 1,
          created_at: {
            [Op.gte]: expect.any(Date),
          },
        }),
      });
    });
  });

  describe("getBulkRecipeHighlights", () => {
    it("should return empty highlights when no recipes provided", async () => {
      const result = await getBulkRecipeHighlights([], "org123");

      expect(result).toEqual({
        highlights: {},
        metadata: {
          totalRecipes: 0,
          recipesWithHighlights: 0,
          queryTimestamp: expect.any(Date),
        },
      });
    });

    it("should process multiple recipes efficiently", async () => {
      const mockResults = [
        {
          recipe_id: 1,
          action: RecipeHistoryAction.ingredient_added,
          description: "Added ingredient",
          created_at: "2024-01-15T10:30:00Z",
          created_by: 123,
          field_name: "ingredient_name",
          rn: 1,
        },
        {
          recipe_id: 2,
          action: RecipeHistoryAction.step_updated,
          description: "Updated step",
          created_at: "2024-01-14T09:20:00Z",
          created_by: 456,
          field_name: "step_description",
          rn: 1,
        },
      ];

      const mockUsers = [
        { user_full_name: "John Doe" },
        { user_full_name: "Jane Smith" },
      ];

      sequelize.query.mockResolvedValue(mockResults);
      getUser.mockImplementation((userId) => {
        return userId === 123 ? mockUsers[0] : mockUsers[1];
      });

      const result = await getBulkRecipeHighlights([1, 2], "org123");

      expect(result.highlights).toHaveProperty("1");
      expect(result.highlights).toHaveProperty("2");
      expect(result.metadata.totalRecipes).toBe(2);
      expect(result.metadata.recipesWithHighlights).toBe(2);
      expect(sequelize.query).toHaveBeenCalledWith(
        expect.stringContaining("ROW_NUMBER()"),
        expect.objectContaining({
          type: QueryTypes.SELECT,
        })
      );
    });

    it("should handle database errors gracefully", async () => {
      sequelize.query.mockRejectedValue(new Error("Database error"));

      const result = await getBulkRecipeHighlights([1, 2], "org123");

      expect(result).toEqual({
        highlights: {},
        metadata: {
          totalRecipes: 2,
          recipesWithHighlights: 0,
          queryTimestamp: expect.any(Date),
        },
      });
    });
  });
});
