/**
 * Helper functions for recipe highlight feature
 * Analyzes RecipeHistory table to determine what was last updated in recipes
 */

import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";
import { RecipeHistory, RecipeHistoryAction } from "../models/RecipeHistory";
import { getUser } from "./common";

// Component types that can be highlighted in a recipe
const RecipeComponentType = {
  BASIC_INFO: "basic_info",
  INGREDIENTS: "ingredients",
  STEPS: "steps",
  CATEGORIES: "categories",
  ATTRIBUTES: "attributes",
  RESOURCES: "resources",
  STATUS: "status",
  GENERAL: "general"
};

// Highlight priority levels for different types of changes
const HighlightPriority = {
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
  CRITICAL: 4
};

// Mapping of RecipeHistoryAction to component types
const ACTION_TO_COMPONENT_MAP = {
  // Basic recipe information
  [RecipeHistoryAction.created]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.updated]: RecipeComponentType.BASIC_INFO,
  [RecipeHistoryAction.published]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.archived]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.restored]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.deleted]: RecipeComponentType.STATUS,

  // Ingredients
  [RecipeHistoryAction.ingredient_added]: RecipeComponentType.INGREDIENTS,
  [RecipeHistoryAction.ingredient_removed]: RecipeComponentType.INGREDIENTS,
  [RecipeHistoryAction.ingredient_updated]: RecipeComponentType.INGREDIENTS,

  // Steps
  [RecipeHistoryAction.step_added]: RecipeComponentType.STEPS,
  [RecipeHistoryAction.step_removed]: RecipeComponentType.STEPS,
  [RecipeHistoryAction.step_updated]: RecipeComponentType.STEPS,

  // Categories
  [RecipeHistoryAction.category_added]: RecipeComponentType.CATEGORIES,
  [RecipeHistoryAction.category_removed]: RecipeComponentType.CATEGORIES,

  // Attributes
  [RecipeHistoryAction.attribute_added]: RecipeComponentType.ATTRIBUTES,
  [RecipeHistoryAction.attribute_removed]: RecipeComponentType.ATTRIBUTES,

  // Resources
  [RecipeHistoryAction.resource_added]: RecipeComponentType.RESOURCES,
  [RecipeHistoryAction.resource_removed]: RecipeComponentType.RESOURCES,

  // Bookmarks (not highlighted as they're user-specific)
  [RecipeHistoryAction.bookmark_added]: RecipeComponentType.GENERAL,
  [RecipeHistoryAction.bookmark_removed]: RecipeComponentType.GENERAL,
};

// Priority mapping for different actions
const ACTION_PRIORITY_MAP = {
  // High priority - structural changes
  [RecipeHistoryAction.created]: HighlightPriority.HIGH,
  [RecipeHistoryAction.published]: HighlightPriority.HIGH,
  [RecipeHistoryAction.archived]: HighlightPriority.CRITICAL,
  [RecipeHistoryAction.deleted]: HighlightPriority.CRITICAL,
  [RecipeHistoryAction.restored]: HighlightPriority.HIGH,

  // Medium priority - content changes
  [RecipeHistoryAction.updated]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_added]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_removed]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_updated]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_added]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_removed]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_updated]: HighlightPriority.MEDIUM,

  // Low priority - metadata changes
  [RecipeHistoryAction.category_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.category_removed]: HighlightPriority.LOW,
  [RecipeHistoryAction.attribute_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.attribute_removed]: HighlightPriority.LOW,
  [RecipeHistoryAction.resource_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.resource_removed]: HighlightPriority.LOW,

  // Very low priority - user actions
  [RecipeHistoryAction.bookmark_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.bookmark_removed]: HighlightPriority.LOW,
};

// Component display names for UI
const COMPONENT_DISPLAY_NAMES = {
  [RecipeComponentType.BASIC_INFO]: "Basic Information",
  [RecipeComponentType.INGREDIENTS]: "Ingredients",
  [RecipeComponentType.STEPS]: "Preparation Steps",
  [RecipeComponentType.CATEGORIES]: "Categories",
  [RecipeComponentType.ATTRIBUTES]: "Attributes",
  [RecipeComponentType.RESOURCES]: "Resources",
  [RecipeComponentType.STATUS]: "Recipe Status",
  [RecipeComponentType.GENERAL]: "General",
};

// Actions that should be excluded from highlighting (e.g., bookmarks are user-specific)
const EXCLUDED_HIGHLIGHT_ACTIONS = [
  RecipeHistoryAction.bookmark_added,
  RecipeHistoryAction.bookmark_removed,
];

// Default highlight configuration
const DEFAULT_HIGHLIGHT_CONFIG = {
  maxHighlightAgeDays: 30, // Show highlights for changes within last 30 days
  includeUserActions: false, // Don't include bookmarks in highlights
  minPriorityLevel: HighlightPriority.LOW, // Show all priority levels
};

/**
 * Get the most recent highlight for a single recipe
 */
export const getRecipeHighlight = async (
  recipeId: number,
  organizationId: string | null,
  config = DEFAULT_HIGHLIGHT_CONFIG
) => {
  try {
    // Calculate the cutoff date for highlights
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);

    // Build where conditions
    const whereConditions: any = {
      recipe_id: recipeId,
      created_at: {
        [Op.gte]: cutoffDate,
      },
    };

    if (organizationId) {
      whereConditions.organization_id = organizationId;
    }

    // Exclude certain actions if configured
    if (!config.includeUserActions) {
      whereConditions.action = {
        [Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
      };
    }

    // Get the most recent history entry
    const latestHistory = await RecipeHistory.findOne({
      where: whereConditions,
      order: [["created_at", "DESC"]],
      limit: 1,
    });

    if (!latestHistory) {
      return null;
    }

    // Check if the action meets minimum priority level
    const actionPriority = (ACTION_PRIORITY_MAP as any)[latestHistory.action];
    if (actionPriority < config.minPriorityLevel) {
      return null;
    }

    // Get user information
    const userInfo = await getUser(latestHistory.created_by);

    // Map action to component type
    const component = (ACTION_TO_COMPONENT_MAP as any)[latestHistory.action];

    // Create highlight object
    const highlight = {
      component,
      action: latestHistory.action,
      description: latestHistory.description || generateDefaultDescription(latestHistory.action, component),
      lastModified: latestHistory.created_at,
      modifiedBy: {
        userId: latestHistory.created_by,
        userName: userInfo?.user_full_name || userInfo?.user_first_name || userInfo?.user_email || "Unknown User",
      },
      priority: actionPriority,
      context: {
        fieldName: latestHistory.field_name,
      },
    };

    return highlight;
  } catch (error) {
    console.error("Error getting recipe highlight:", error);
    return null;
  }
};

/**
 * Get highlights for multiple recipes efficiently
 */
export const getBulkRecipeHighlights = async (
  recipeIds: number[],
  organizationId: string | null,
  config = DEFAULT_HIGHLIGHT_CONFIG
) => {
  try {
    if (recipeIds.length === 0) {
      return {
        highlights: {},
        metadata: {
          totalRecipes: 0,
          recipesWithHighlights: 0,
          queryTimestamp: new Date(),
        },
      };
    }

    // Calculate the cutoff date for highlights
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);

    // Build the SQL query to get the most recent history entry for each recipe
    const excludedActions = config.includeUserActions ? [] : EXCLUDED_HIGHLIGHT_ACTIONS;
    const excludedActionsClause = excludedActions.length > 0
      ? `AND rh.action NOT IN (${excludedActions.map(a => `'${a}'`).join(', ')})`
      : '';

    const organizationClause = organizationId
      ? `AND rh.organization_id = :organizationId`
      : '';

    const query = `
      SELECT 
        rh.recipe_id,
        rh.action,
        rh.description,
        rh.field_name,
        rh.created_at,
        rh.created_by,
        ROW_NUMBER() OVER (PARTITION BY rh.recipe_id ORDER BY rh.created_at DESC) as rn
      FROM mo_recipe_history rh
      WHERE rh.recipe_id IN (${recipeIds.map((id: number) => `${id}`).join(', ')})
        AND rh.created_at >= :cutoffDate
        ${excludedActionsClause}
        ${organizationClause}
    `;

    const results = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        cutoffDate: cutoffDate.toISOString(),
        ...(organizationId && { organizationId }),
      },
    });

    // Filter to get only the most recent entry per recipe and apply priority filter
    const latestEntries = results.filter((row: any) => {
      if (row.rn !== 1) return false; // Only the most recent entry

      const actionPriority = (ACTION_PRIORITY_MAP as any)[row.action];
      return actionPriority >= config.minPriorityLevel;
    });

    // Get user information for all unique user IDs
    const userIds = [...new Set(latestEntries.map((entry: any) => entry.created_by))];
    const userInfoMap = new Map();

    for (const userId of userIds) {
      const userInfo = await getUser(userId);
      userInfoMap.set(userId, userInfo);
    }

    // Build highlights map
    const highlights: any = {};

    for (const entry of latestEntries) {
      const userInfo = userInfoMap.get(entry.created_by);
      const component = (ACTION_TO_COMPONENT_MAP as any)[entry.action];
      const actionPriority = (ACTION_PRIORITY_MAP as any)[entry.action];

      highlights[entry.recipe_id] = {
        component,
        action: entry.action,
        description: entry.description || generateDefaultDescription(entry.action, component),
        lastModified: new Date(entry.created_at),
        modifiedBy: {
          userId: entry.created_by,
          userName: userInfo?.user_full_name || userInfo?.user_first_name || userInfo?.user_email || "Unknown User",
        },
        priority: actionPriority,
        context: {
          fieldName: entry.field_name,
        },
      };
    }

    return {
      highlights,
      metadata: {
        totalRecipes: recipeIds.length,
        recipesWithHighlights: Object.keys(highlights).length,
        queryTimestamp: new Date(),
      },
    };
  } catch (error) {
    console.error("Error getting bulk recipe highlights:", error);
    return {
      highlights: {},
      metadata: {
        totalRecipes: recipeIds.length,
        recipesWithHighlights: 0,
        queryTimestamp: new Date(),
      },
    };
  }
};



/**
 * Generate a default description for an action if none exists
 */
function generateDefaultDescription(action: string, component: string) {
  const componentName = COMPONENT_DISPLAY_NAMES[component];

  switch (action) {
    case RecipeHistoryAction.created:
      return "Recipe was created";
    case RecipeHistoryAction.updated:
      return `${componentName} was updated`;
    case RecipeHistoryAction.published:
      return "Recipe was published";
    case RecipeHistoryAction.archived:
      return "Recipe was archived";
    case RecipeHistoryAction.restored:
      return "Recipe was restored";
    case RecipeHistoryAction.deleted:
      return "Recipe was deleted";
    case RecipeHistoryAction.ingredient_added:
      return "New ingredient was added";
    case RecipeHistoryAction.ingredient_removed:
      return "Ingredient was removed";
    case RecipeHistoryAction.ingredient_updated:
      return "Ingredient was updated";
    case RecipeHistoryAction.step_added:
      return "New preparation step was added";
    case RecipeHistoryAction.step_removed:
      return "Preparation step was removed";
    case RecipeHistoryAction.step_updated:
      return "Preparation step was updated";
    case RecipeHistoryAction.category_added:
      return "Category was added";
    case RecipeHistoryAction.category_removed:
      return "Category was removed";
    case RecipeHistoryAction.attribute_added:
      return "Attribute was added";
    case RecipeHistoryAction.attribute_removed:
      return "Attribute was removed";
    case RecipeHistoryAction.resource_added:
      return "Resource was added";
    case RecipeHistoryAction.resource_removed:
      return "Resource was removed";
    default:
      return `${componentName} was modified`;
  }
}

/**
 * Check if a recipe has recent changes within the specified timeframe
 */
export const hasRecentChanges = async (
  recipeId: number,
  organizationId: string | null,
  daysBack: number = 7
): Promise<boolean> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysBack);

    const whereConditions: any = {
      recipe_id: recipeId,
      created_at: {
        [Op.gte]: cutoffDate,
      },
      action: {
        [Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
      },
    };

    if (organizationId) {
      whereConditions.organization_id = organizationId;
    }

    const count = await RecipeHistory.count({
      where: whereConditions,
    });

    return count > 0;
  } catch (error) {
    console.error("Error checking recent changes:", error);
    return false;
  }
};
