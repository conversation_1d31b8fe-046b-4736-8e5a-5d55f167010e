import swaggerJsdoc from "swagger-jsdoc";

const options = {
  definition: {
    openapi: "3.0.3",
    info: {
      title: "Recipe Management API",
      description: `
        Comprehensive Recipe Management System API with advanced features:

        ## Features
        - Complete CRUD operations for Categories, Food Attributes, Recipe Measures, and Ingredients
        - Advanced file upload system with S3/MinIO integration
        - Multi-tenant organization support
        - Role-based access control
        - Comprehensive search and filtering
        - Excel import/export functionality
        - Bulk operations support

        ## Authentication
        All private endpoints require Bearer token authentication.

        ## File Upload
        - Supports multiple file formats (images, videos, documents)
        - Automatic duplicate detection using SHA-256 hashing
        - Organization-based file isolation
        - S3/MinIO cloud storage integration
      `,
      version: "1.0.0",
      contact: {
        name: "Recipe API Support",
        email: "<EMAIL>",
      },
      license: {
        name: "MIT",
        url: "https://opensource.org/licenses/MIT",
      },
    },
    servers: [
      {
        url: "http://localhost:9025/v1",
        description: "Development server",
      },
      {
        url: "http://staging.namastevillage.theeasyaccess.com/recipe-api/v1",
        description: "Staging server",
      },
    ],
    tags: [
      {
        name: "Authentication",
        description: "Authentication related endpoints",
      },
      {
        name: "Analytics",
        description: "Private analytics endpoints for dashboard and reporting",
      },
      {
        name: "Public Analytics",
        description:
          "Public analytics tracking endpoints (no authentication required)",
      },
      {
        name: "Dashboard",
        description: "Dashboard overview and statistics endpoints",
      },
      {
        name: "Recipes",
        description: "Recipe management endpoints",
      },
      {
        name: "Categories",
        description: "Category management endpoints",
      },
      {
        name: "Ingredients",
        description: "Ingredient management endpoints",
      },
      {
        name: "Food Attributes",
        description: "Food attribute management endpoints",
      },
      {
        name: "Recipe Measures",
        description: "Recipe measure management endpoints",
      },
      {
        name: "Contact Us",
        description: "Contact us related endpoints",
      },
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "Enter your Bearer token in the format: Bearer <token>",
        },
      },
      schemas: {
        // Common schemas
        Error: {
          type: "object",
          properties: {
            status: {
              type: "boolean",
              example: false,
            },
            message: {
              type: "string",
              example: "Error message",
            },
            error: {
              type: "string",
              example: "Detailed error information",
            },
          },
        },
        Success: {
          type: "object",
          properties: {
            status: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Operation successful",
            },
            data: {
              type: "object",
            },
          },
        },
        Pagination: {
          type: "object",
          properties: {
            total: {
              type: "integer",
              example: 100,
            },
            page: {
              type: "integer",
              example: 1,
            },
            limit: {
              type: "integer",
              example: 10,
            },
            totalPages: {
              type: "integer",
              example: 10,
            },
            hasNextPage: {
              type: "boolean",
              example: true,
            },
            hasPrevPage: {
              type: "boolean",
              example: false,
            },
          },
        },
        // Category schemas
        Category: {
          type: "object",
          properties: {
            id: {
              type: "integer",
              example: 1,
            },
            category_name: {
              type: "string",
              example: "Vegetables",
            },
            category_slug: {
              type: "string",
              example: "vegetables",
            },
            category_description: {
              type: "string",
              example: "Fresh vegetables and greens",
            },
            category_icon: {
              type: "integer",
              nullable: true,
              example: 123,
            },
            category_status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            category_type: {
              type: "string",
              enum: ["recipe", "ingredient"],
              example: "ingredient",
            },
            organization_id: {
              type: "string",
              nullable: true,
              example: "org_123",
            },
            is_system_category: {
              type: "boolean",
              example: false,
            },
            created_by: {
              type: "integer",
              example: 1,
            },
            updated_by: {
              type: "integer",
              example: 1,
            },
            created_at: {
              type: "string",
              format: "date-time",
              example: "2024-01-15T10:30:00Z",
            },
            updated_at: {
              type: "string",
              format: "date-time",
              example: "2024-01-15T10:30:00Z",
            },
            iconUrl: {
              type: "string",
              nullable: true,
              example: "https://example.com/icon.jpg",
            },
            hasIcon: {
              type: "boolean",
              example: true,
            },
          },
        },
        CategoryCreate: {
          type: "object",
          required: ["category_name", "category_type"],
          properties: {
            category_name: {
              type: "string",
              example: "Vegetables",
              minLength: 2,
              maxLength: 100,
            },
            category_description: {
              type: "string",
              example: "Fresh vegetables and greens",
            },
            category_status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            category_type: {
              type: "string",
              enum: ["recipe", "ingredient"],
              example: "ingredient",
            },
          },
        },
        FoodAttributeCreate: {
          type: "object",
          required: ["attribute_title", "attribute_type"],
          properties: {
            attribute_title: {
              type: "string",
              example: "Protein",
              minLength: 2,
              maxLength: 100,
            },
            attribute_description: {
              type: "string",
              example: "High protein content",
            },
            attribute_status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            attribute_type: {
              type: "string",
              enum: ["nutrition", "allergen", "cuisine", "dietary"],
              example: "nutrition",
            },
          },
        },
        RecipeMeasureCreate: {
          type: "object",
          required: ["unit_title"],
          properties: {
            unit_title: {
              type: "string",
              example: "Grams",
              minLength: 2,
              maxLength: 100,
            },
            status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
          },
        },
        // Food Attributes schemas
        FoodAttribute: {
          type: "object",
          properties: {
            id: {
              type: "integer",
              example: 1,
            },
            attribute_title: {
              type: "string",
              example: "Protein",
            },
            attribute_slug: {
              type: "string",
              example: "protein",
            },
            attribute_description: {
              type: "string",
              example: "High protein content",
            },
            attribute_type: {
              type: "string",
              enum: ["nutrition", "allergen", "cuisine", "dietary"],
              example: "nutrition",
            },
            attribute_icon: {
              type: "integer",
              nullable: true,
              example: 456,
            },
            attribute_status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            organization_id: {
              type: "string",
              nullable: true,
              example: "org_123",
            },
            is_system_attribute: {
              type: "boolean",
              example: false,
            },
            created_by: {
              type: "integer",
              example: 1,
            },
            updated_by: {
              type: "integer",
              example: 1,
            },
            created_at: {
              type: "string",
              format: "date-time",
            },
            updated_at: {
              type: "string",
              format: "date-time",
            },
          },
        },
        // Recipe Measure schemas
        RecipeMeasure: {
          type: "object",
          properties: {
            id: {
              type: "integer",
              example: 1,
            },
            unit_title: {
              type: "string",
              example: "Grams",
            },
            unit_slug: {
              type: "string",
              example: "grams",
            },
            unit_description: {
              type: "string",
              example: "Weight measurement in grams",
            },
            unit_icon: {
              type: "integer",
              nullable: true,
              example: 789,
            },
            status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            organization_id: {
              type: "string",
              nullable: true,
              example: "org_123",
            },
            is_system_unit: {
              type: "boolean",
              example: false,
            },
            created_by: {
              type: "integer",
              example: 1,
            },
            updated_by: {
              type: "integer",
              example: 1,
            },
            created_at: {
              type: "string",
              format: "date-time",
            },
            updated_at: {
              type: "string",
              format: "date-time",
            },
          },
        },
        // Ingredient schemas
        Ingredient: {
          type: "object",
          properties: {
            id: {
              type: "integer",
              example: 1,
            },
            ingredient_name: {
              type: "string",
              example: "Chicken Breast",
            },
            ingredient_description: {
              type: "string",
              example: "Fresh organic chicken breast",
            },
            ingredient_status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            cost: {
              type: "number",
              format: "decimal",
              example: 12.99,
            },
            waste_percentage: {
              type: "number",
              format: "decimal",
              example: 5.5,
            },
            measure_of_cost: {
              type: "object",
              properties: {
                id: {
                  type: "integer",
                  example: 1,
                },
                unit_title: {
                  type: "string",
                  example: "Grams",
                },
              },
            },
            category: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: {
                    type: "integer",
                    example: 1,
                  },
                  category_name: {
                    type: "string",
                    example: "Protein",
                  },
                },
              },
            },
            allergy: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: {
                    type: "integer",
                    example: 1,
                  },
                  attribute_title: {
                    type: "string",
                    example: "Gluten Free",
                  },
                },
              },
            },
            dietary_info: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: {
                    type: "integer",
                    example: 1,
                  },
                  attribute_title: {
                    type: "string",
                    example: "Organic",
                  },
                },
              },
            },
            cuisine: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: {
                    type: "integer",
                    example: 1,
                  },
                  attribute_title: {
                    type: "string",
                    example: "Mediterranean",
                  },
                },
              },
            },
            created_by: {
              type: "integer",
              example: 1,
            },
            updated_by: {
              type: "integer",
              example: 1,
            },
            created_at: {
              type: "string",
              format: "date-time",
            },
            updated_at: {
              type: "string",
              format: "date-time",
            },
          },
        },
        IngredientCreate: {
          type: "object",
          required: [
            "ingredient_name",
            "ingredient_status",
            "cost_per_unit",
            "unit_of_measure",
          ],
          properties: {
            ingredient_name: {
              type: "string",
              example: "Chicken Breast",
              minLength: 2,
              maxLength: 100,
            },
            ingredient_description: {
              type: "string",
              example: "Fresh organic chicken breast",
            },
            ingredient_status: {
              type: "string",
              enum: ["active", "inactive"],
              example: "active",
            },
            cost_per_unit: {
              type: "number",
              format: "decimal",
              example: 12.99,
            },
            waste_percentage: {
              type: "number",
              format: "decimal",
              example: 5.5,
            },
            unit_of_measure: {
              type: "integer",
              example: 1,
              description: "Recipe measure ID",
            },
            categories: {
              type: "array",
              items: {
                type: "integer",
              },
              example: [1, 2],
              description: "Array of category IDs",
            },
            nutritions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: {
                    type: "integer",
                    example: 1,
                  },
                  unit_of_measure: {
                    type: "integer",
                    example: 2,
                  },
                  unit: {
                    type: "number",
                    example: 25.5,
                  },
                },
              },
            },
            allergens: {
              type: "array",
              items: {
                type: "integer",
              },
              example: [4, 5],
              description: "Array of allergen attribute IDs",
            },
            dietary: {
              type: "array",
              items: {
                type: "integer",
              },
              example: [6, 7],
              description: "Array of dietary attribute IDs",
            },
            conversions: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  from_measure: {
                    type: "integer",
                    example: 1,
                  },
                  from_measure_value: {
                    type: "number",
                    example: 100,
                  },
                  to_measure: {
                    type: "integer",
                    example: 2,
                  },
                  to_measure_value: {
                    type: "number",
                    example: 0.1,
                  },
                },
              },
            },
          },
        },
        // Analytics schemas
        AnalyticsEvent: {
          type: "object",
          properties: {
            event_type: {
              type: "string",
              enum: [
                "recipe_view",
                "cta_click",
                "contact_form_submit",
                "recipe_download",
                "recipe_share",
                "dashboard_view",
              ],
              example: "recipe_view",
            },
            entity_type: {
              type: "string",
              enum: [
                "recipe",
                "category",
                "ingredient",
                "dashboard",
                "settings",
              ],
              example: "recipe",
            },
            entity_id: {
              type: "number",
              example: 123,
            },
            session_id: {
              type: "string",
              example: "sess_abc123",
            },
            metadata: {
              type: "object",
              example: {
                recipe_name: "Chocolate Cake",
                view_duration: 45,
              },
            },
          },
          required: ["event_type", "entity_type"],
        },
        PublicAnalyticsEvent: {
          type: "object",
          properties: {
            event_type: {
              type: "string",
              enum: ["recipe_view", "cta_click", "contact_form_submit"],
              example: "recipe_view",
            },
            entity_type: {
              type: "string",
              enum: ["recipe", "category"],
              example: "recipe",
            },
            entity_id: {
              type: "number",
              example: 123,
            },
            organization_id: {
              type: "string",
              example: "org_123",
            },
            session_id: {
              type: "string",
              example: "sess_abc123",
            },
            metadata: {
              type: "object",
              example: {
                recipe_name: "Chocolate Cake",
                view_duration: 45,
              },
            },
          },
          required: ["event_type", "entity_type"],
        },
        CtaClickAnalytics: {
          type: "object",
          properties: {
            recipe_id: {
              type: "number",
              example: 123,
            },
            recipe_name: {
              type: "string",
              example: "Chocolate Cake",
            },
            cta_type: {
              type: "string",
              enum: ["contact_info", "contact_form", "custom_cta"],
              example: "contact_form",
            },
            clicks: {
              type: "number",
              example: 25,
            },
            last_clicked_at: {
              type: "string",
              format: "date-time",
              example: "2024-01-15T10:30:00Z",
            },
          },
        },
        ContactSubmission: {
          type: "object",
          properties: {
            id: {
              type: "number",
              example: 1,
            },
            recipe_id: {
              type: "number",
              example: 123,
            },
            recipe_name: {
              type: "string",
              example: "Chocolate Cake",
            },
            name: {
              type: "string",
              example: "John Doe",
            },
            email: {
              type: "string",
              example: "<EMAIL>",
            },
            mobile: {
              type: "string",
              example: "+1234567890",
            },
            message: {
              type: "string",
              example: "I want the complete recipe",
            },
            submitted_on: {
              type: "string",
              format: "date-time",
              example: "2024-01-15T10:30:00Z",
            },
          },
        },
        DashboardOverview: {
          type: "object",
          properties: {
            totalRecipes: {
              type: "number",
              example: 150,
            },
            totalIngredients: {
              type: "number",
              example: 500,
            },
            totalCategories: {
              type: "number",
              example: 25,
            },
            totalFoodAttributes: {
              type: "number",
              example: 40,
            },
            totalRecipeMeasures: {
              type: "number",
              example: 30,
            },
            totalPublicRecipes: {
              type: "number",
              example: 75,
            },
            totalViews: {
              type: "number",
              example: 1250,
            },
            totalCtaClicks: {
              type: "number",
              example: 85,
            },
            totalContactSubmissions: {
              type: "number",
              example: 45,
            },
            topViewedRecipes: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  recipe_id: { type: "number" },
                  recipe_name: { type: "string" },
                  total_views: { type: "number" },
                },
              },
            },
            topClickedRecipes: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  recipe_id: { type: "number" },
                  recipe_name: { type: "string" },
                  total_clicks: { type: "number" },
                },
              },
            },
            recentActivity: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  event_type: { type: "string" },
                  entity_type: { type: "string" },
                  created_at: { type: "string", format: "date-time" },
                },
              },
            },
          },
        },
        // Recipe Highlight schemas
        RecipeHighlight: {
          type: "object",
          properties: {
            component: {
              type: "string",
              enum: ["basic_info", "ingredients", "steps", "categories", "attributes", "resources", "status", "general"],
              example: "ingredients",
              description: "The component that was last modified"
            },
            action: {
              type: "string",
              enum: [
                "created", "updated", "published", "archived", "restored", "deleted",
                "ingredient_added", "ingredient_removed", "ingredient_updated",
                "step_added", "step_removed", "step_updated",
                "category_added", "category_removed",
                "attribute_added", "attribute_removed",
                "resource_added", "resource_removed",
                "bookmark_added", "bookmark_removed"
              ],
              example: "ingredient_added",
              description: "The specific action that was performed"
            },
            description: {
              type: "string",
              example: "New ingredient was added",
              description: "Human-readable description of what changed"
            },
            lastModified: {
              type: "string",
              format: "date-time",
              example: "2024-01-15T10:30:00Z",
              description: "When the change occurred"
            },
            modifiedBy: {
              type: "object",
              properties: {
                userId: {
                  type: "integer",
                  example: 123,
                  description: "ID of the user who made the change"
                },
                userName: {
                  type: "string",
                  example: "John Doe",
                  description: "Name of the user who made the change"
                }
              },
              required: ["userId"],
              description: "Information about who made the change"
            },
            priority: {
              type: "integer",
              enum: [1, 2, 3, 4],
              example: 2,
              description: "Priority level for UI highlighting (1=Low, 2=Medium, 3=High, 4=Critical)"
            },
            context: {
              type: "object",
              properties: {
                fieldName: {
                  type: "string",
                  example: "ingredient_name",
                  description: "Specific field that was changed"
                },
                changeCount: {
                  type: "integer",
                  example: 3,
                  description: "Number of changes made"
                },
                affectedItems: {
                  type: "array",
                  items: {
                    type: "string"
                  },
                  example: ["Chicken Breast", "Salt", "Pepper"],
                  description: "List of items that were affected"
                }
              },
              description: "Additional context about the change"
            }
          },
          required: ["component", "action", "description", "lastModified", "modifiedBy", "priority"],
          description: "Information about the most recent change to a recipe component"
        },
      },
    },
    security: [
      {
        BearerAuth: [],
      },
    ],
  },
  apis: [
    "./src/routes/private/*.ts",
    "./src/routes/public/*.ts",
    "./src/controller/*.ts",
    "./src/routes/private/analytics.routes.ts",
    "./src/routes/private/dashboard.routes.ts",
    "./src/routes/public/analytics.routes.ts",
  ],
};

export const swaggerSpec = swaggerJsdoc(options);
