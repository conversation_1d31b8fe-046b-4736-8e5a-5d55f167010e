/**
 * Test runner for recipe highlight feature
 * This script runs both unit and integration tests for the highlight functionality
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Recipe Highlight Feature Tests\n');

// Test configuration
const testConfig = {
  testTimeout: 30000,
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
};

// Build Jest command
const jestCommand = [
  'npx jest',
  '--testTimeout=' + testConfig.testTimeout,
  testConfig.verbose ? '--verbose' : '',
  testConfig.detectOpenHandles ? '--detectOpenHandles' : '',
  testConfig.forceExit ? '--forceExit' : '',
  '--testPathPattern="recipe-highlight"',
].filter(Boolean).join(' ');

try {
  console.log('📋 Test Configuration:');
  console.log(`   - Timeout: ${testConfig.testTimeout}ms`);
  console.log(`   - Verbose: ${testConfig.verbose}`);
  console.log(`   - Detect Open Handles: ${testConfig.detectOpenHandles}`);
  console.log(`   - Force Exit: ${testConfig.forceExit}`);
  console.log(`   - Pattern: recipe-highlight\n`);

  console.log('🚀 Starting tests...\n');
  
  // Run the tests
  execSync(jestCommand, {
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..'),
  });

  console.log('\n✅ All tests completed successfully!');
  
} catch (error) {
  console.error('\n❌ Tests failed with error:');
  console.error(error.message);
  process.exit(1);
}
