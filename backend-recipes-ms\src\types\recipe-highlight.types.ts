/**
 * Types and interfaces for recipe highlight feature
 * This feature shows what was last updated in a recipe by analyzing the RecipeHistory table
 */

import { RecipeHistoryAction } from "../models/RecipeHistory";

/**
 * Component types that can be highlighted in a recipe
 */
export enum RecipeComponentType {
  BASIC_INFO = "basic_info",
  INGREDIENTS = "ingredients", 
  STEPS = "steps",
  CATEGORIES = "categories",
  ATTRIBUTES = "attributes",
  RESOURCES = "resources",
  STATUS = "status",
  GENERAL = "general"
}

/**
 * Highlight priority levels for different types of changes
 */
export enum HighlightPriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}

/**
 * Main highlight information for a recipe
 */
export interface RecipeHighlight {
  /** The component that was last modified */
  component: RecipeComponentType;
  
  /** The specific action that was performed */
  action: RecipeHistoryAction;
  
  /** Human-readable description of what changed */
  description: string;
  
  /** When the change occurred */
  lastModified: Date;
  
  /** Who made the change */
  modifiedBy: {
    userId: number;
    userName?: string;
  };
  
  /** Priority level for UI highlighting */
  priority: HighlightPriority;
  
  /** Additional context about the change */
  context?: {
    fieldName?: string;
    changeCount?: number;
    affectedItems?: string[];
  };
}

/**
 * Mapping of RecipeHistoryAction to component types
 */
export const ACTION_TO_COMPONENT_MAP: Record<RecipeHistoryAction, RecipeComponentType> = {
  // Basic recipe information
  [RecipeHistoryAction.created]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.updated]: RecipeComponentType.BASIC_INFO,
  [RecipeHistoryAction.published]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.archived]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.restored]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.deleted]: RecipeComponentType.STATUS,
  
  // Ingredients
  [RecipeHistoryAction.ingredient_added]: RecipeComponentType.INGREDIENTS,
  [RecipeHistoryAction.ingredient_removed]: RecipeComponentType.INGREDIENTS,
  [RecipeHistoryAction.ingredient_updated]: RecipeComponentType.INGREDIENTS,
  
  // Steps
  [RecipeHistoryAction.step_added]: RecipeComponentType.STEPS,
  [RecipeHistoryAction.step_removed]: RecipeComponentType.STEPS,
  [RecipeHistoryAction.step_updated]: RecipeComponentType.STEPS,
  
  // Categories
  [RecipeHistoryAction.category_added]: RecipeComponentType.CATEGORIES,
  [RecipeHistoryAction.category_removed]: RecipeComponentType.CATEGORIES,
  
  // Attributes
  [RecipeHistoryAction.attribute_added]: RecipeComponentType.ATTRIBUTES,
  [RecipeHistoryAction.attribute_removed]: RecipeComponentType.ATTRIBUTES,
  
  // Resources
  [RecipeHistoryAction.resource_added]: RecipeComponentType.RESOURCES,
  [RecipeHistoryAction.resource_removed]: RecipeComponentType.RESOURCES,
  
  // Bookmarks (not highlighted as they're user-specific)
  [RecipeHistoryAction.bookmark_added]: RecipeComponentType.GENERAL,
  [RecipeHistoryAction.bookmark_removed]: RecipeComponentType.GENERAL,
};

/**
 * Priority mapping for different actions
 */
export const ACTION_PRIORITY_MAP: Record<RecipeHistoryAction, HighlightPriority> = {
  // High priority - structural changes
  [RecipeHistoryAction.created]: HighlightPriority.HIGH,
  [RecipeHistoryAction.published]: HighlightPriority.HIGH,
  [RecipeHistoryAction.archived]: HighlightPriority.CRITICAL,
  [RecipeHistoryAction.deleted]: HighlightPriority.CRITICAL,
  [RecipeHistoryAction.restored]: HighlightPriority.HIGH,
  
  // Medium priority - content changes
  [RecipeHistoryAction.updated]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_added]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_removed]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_updated]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_added]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_removed]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_updated]: HighlightPriority.MEDIUM,
  
  // Low priority - metadata changes
  [RecipeHistoryAction.category_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.category_removed]: HighlightPriority.LOW,
  [RecipeHistoryAction.attribute_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.attribute_removed]: HighlightPriority.LOW,
  [RecipeHistoryAction.resource_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.resource_removed]: HighlightPriority.LOW,
  
  // Very low priority - user actions
  [RecipeHistoryAction.bookmark_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.bookmark_removed]: HighlightPriority.LOW,
};

/**
 * Component display names for UI
 */
export const COMPONENT_DISPLAY_NAMES: Record<RecipeComponentType, string> = {
  [RecipeComponentType.BASIC_INFO]: "Basic Information",
  [RecipeComponentType.INGREDIENTS]: "Ingredients",
  [RecipeComponentType.STEPS]: "Preparation Steps",
  [RecipeComponentType.CATEGORIES]: "Categories",
  [RecipeComponentType.ATTRIBUTES]: "Attributes",
  [RecipeComponentType.RESOURCES]: "Resources",
  [RecipeComponentType.STATUS]: "Recipe Status",
  [RecipeComponentType.GENERAL]: "General",
};

/**
 * Actions that should be excluded from highlighting (e.g., bookmarks are user-specific)
 */
export const EXCLUDED_HIGHLIGHT_ACTIONS: RecipeHistoryAction[] = [
  RecipeHistoryAction.bookmark_added,
  RecipeHistoryAction.bookmark_removed,
];

/**
 * Configuration for highlight feature
 */
export interface HighlightConfig {
  /** Maximum age in days for highlights to be shown */
  maxHighlightAgeDays: number;
  
  /** Whether to include user-specific actions like bookmarks */
  includeUserActions: boolean;
  
  /** Minimum priority level to show highlights */
  minPriorityLevel: HighlightPriority;
}

/**
 * Default highlight configuration
 */
export const DEFAULT_HIGHLIGHT_CONFIG: HighlightConfig = {
  maxHighlightAgeDays: 30, // Show highlights for changes within last 30 days
  includeUserActions: false, // Don't include bookmarks in highlights
  minPriorityLevel: HighlightPriority.LOW, // Show all priority levels
};

/**
 * Extended recipe data with highlight information
 */
export interface RecipeWithHighlight {
  /** Standard recipe data */
  [key: string]: any;
  
  /** Highlight information for the recipe */
  highlight?: RecipeHighlight;
  
  /** Whether this recipe has recent changes */
  hasRecentChanges: boolean;
}

/**
 * Bulk highlight data for multiple recipes
 */
export interface BulkHighlightData {
  /** Map of recipe ID to highlight information */
  highlights: Record<number, RecipeHighlight>;
  
  /** Metadata about the highlight query */
  metadata: {
    totalRecipes: number;
    recipesWithHighlights: number;
    queryTimestamp: Date;
  };
}
