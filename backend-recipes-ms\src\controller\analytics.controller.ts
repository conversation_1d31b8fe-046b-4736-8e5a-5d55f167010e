import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import analyticsService from "../services/analytics.service";
import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import { <PERSON>ida<PERSON><PERSON>elper } from "../helper/validation.helper";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../helper/transaction.helper";

/**
 * Track CTA clicks on public recipes
 * @route POST /api/v1/public/analytics/track/cta-click
 */
const trackCtaClick = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      recipe_id,
      organization_id: bodyOrgId,
      session_id,
      recipe_name,
      cta_type,
      cta_text,
    } = sanitizedBody;

    // Get organization_id from user context or request body
    const organization_id = req.user?.organization_id || bodyOrgId;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id
        }
      });
    }

    // For public endpoints, organization_id can come from body if no user context
    if (!organization_id) {
      return res.status(400).json({
        status: false,
        message: "organization_id is required (provide in request body for public access)",
        received_data: {
          user_org_id: req.user?.organization_id,
          body_org_id: bodyOrgId,
          has_user: !!req.user
        }
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber = typeof recipe_id === 'string' ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (typeof recipeIdNumber !== 'number' || isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0
        }
      });
    }

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.CTA_CLICK,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: organization_id,
      sessionId: session_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: {
        recipe_name,
        cta_type,
        cta_text,
        tracking_source: req.user ? 'authenticated' : 'public',
      },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "CTA click tracked successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error tracking CTA click");
  }
};

/**
 * Submit contact form from public recipes
 * @route POST /api/v1/public/analytics/contact-form
 */
const submitContactForm = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      recipe_id,
      organization_id: bodyOrgId,
      recipe_name,
      name,
      email,
      mobile,
      message,
    } = sanitizedBody;

    // Get organization_id from user context or request body
    const organization_id = req.user?.organization_id || bodyOrgId;

    // Validate required fields
    if (!recipe_id || !name || !email) {
      return res.status(400).json({
        status: false,
        message: "recipe_id, name, and email are required",
        missing_fields: {
          recipe_id: !recipe_id,
          name: !name,
          email: !email
        }
      });
    }

    // For public endpoints, organization_id can come from body if no user context
    if (!organization_id) {
      return res.status(400).json({
        status: false,
        message: "organization_id is required (provide in request body for public access)",
        received_data: {
          user_org_id: req.user?.organization_id,
          body_org_id: bodyOrgId,
          has_user: !!req.user
        }
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber = typeof recipe_id === 'string' ? parseInt(recipe_id) : recipe_id;

    // Validate recipe_id data type
    if (typeof recipeIdNumber !== 'number' || isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number"
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        status: false,
        message: "Invalid email format"
      });
    }

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.CONTACT_FORM_SUBMIT,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: {
        recipe_name,
        contact_name: name,
        contact_email: email,
        contact_mobile: mobile,
        message,
        tracking_source: req.user ? 'authenticated' : 'public',
      },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Contact form submitted successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error submitting contact form");
  }
};

/**
 * Track recipe views on public recipes
 * @route POST /v1/public/analytics/track/recipe-view
 */
const trackRecipeView = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      recipe_id,
      organization_id: bodyOrgId,
      session_id,
      recipe_name,
      view_duration,
      referrer,
      event_type = "recipe_view", // Default to recipe_view, but allow bookmark/share
      share_platform,
    } = sanitizedBody;

    // Get organization_id from user context or request body
    const organization_id = req.user?.organization_id || bodyOrgId;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id
        }
      });
    }

    // For public endpoints, organization_id can come from body if no user context
    if (!organization_id) {
      return res.status(400).json({
        status: false,
        message: "organization_id is required (provide in request body for public access)",
        received_data: {
          user_org_id: req.user?.organization_id,
          body_org_id: bodyOrgId,
          has_user: !!req.user
        }
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber = typeof recipe_id === 'string' ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (typeof recipeIdNumber !== 'number' || isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0
        }
      });
    }

    // Determine event type based on the request
    let analyticsEventType = AnalyticsEventType.RECIPE_VIEW;
    const metadata: any = {
      recipe_name,
      tracking_source: req.user ? 'authenticated' : 'public',
    };

    // Handle different event types
    switch (event_type) {
      case "recipe_bookmark":
        analyticsEventType = AnalyticsEventType.RECIPE_BOOKMARK;
        metadata.bookmark_action = "add";
        break;
      case "recipe_share":
        analyticsEventType = AnalyticsEventType.RECIPE_SHARE;
        metadata.share_platform = share_platform || "unknown";
        break;
      default:
        analyticsEventType = AnalyticsEventType.RECIPE_VIEW;
        metadata.view_duration = view_duration;
        metadata.referrer = referrer;
        metadata.timestamp = new Date().toISOString();
        break;
    }

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: analyticsEventType,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: organization_id,
      sessionId: session_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: metadata,
    });

    const messages = {
      recipe_view: "Recipe view tracked successfully",
      recipe_bookmark: "Recipe bookmark tracked successfully", 
      recipe_share: "Recipe share tracked successfully"
    };

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: messages[event_type as keyof typeof messages] || messages.recipe_view,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error tracking recipe activity");
  }
};

/**
 * Get CTA click analytics with pagination
 * @route GET /api/v1/private/analytics/cta-clicks
 */
const getCtaClickAnalytics = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range,
      start_date,
      end_date,
      sort = "desc",
      page,
      limit,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    // Admin users can access all data, regular users need organization context
    const isAdmin = await ValidationHelper.isAdminUser(req.user);
    if (!isAdmin && !req.user?.organization_id) {
      return res.status(401).json({
        status: false,
        message: "Organization access required"
      });
    }

    // Validate date_range parameter if provided
    if (date_range) {
      const validDateRanges = ["last_7_days", "last_30_days", "last_90_days", "custom"];
      if (!validDateRanges.includes(date_range)) {
        return res.status(400).json({
          status: false,
          message: "Invalid date_range. Must be one of: " + validDateRanges.join(", ")
        });
      }
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page) : undefined;
    const limitNumber = limit ? parseInt(limit) : undefined;

    // Validate pagination parameters
    if (pageNumber && pageNumber < 1) {
      return res.status(400).json({
        status: false,
        message: "Page number must be greater than 0"
      });
    }

    if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
      return res.status(400).json({
        status: false,
        message: "Limit must be between 1 and 100"
      });
    }

    const result = await analyticsService.getCtaClickAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      pageNumber,
      limitNumber
    );

    // Sort results if no pagination (pagination already handles sorting)
    if (!pageNumber && !limitNumber) {
      if (sort === "asc") {
        result.data.sort((a: any, b: any) => a.clicks - b.clicks);
      } else {
        result.data.sort((a: any, b: any) => b.clicks - a.clicks);
      }
    }

    const response: any = {
      status: true,
      message: res.__("CTA_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: result.data,
      meta: {
        date_range: date_range || null,
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: result.total,
      },
    };

    // Add pagination info if pagination was used
    if (result.pagination) {
      response.pagination = result.pagination;
    }

    return res.status(StatusCodes.OK).json(response);
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error fetching CTA analytics");
  }
};

/**
 * Get contact form submission analytics with pagination
 * @route GET /api/v1/private/analytics/contact-submissions
 */
const getContactSubmissionAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range,
      start_date,
      end_date,
      recipe_id,
      page,
      limit,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    // Validate date_range parameter if provided
    if (date_range) {
      const validDateRanges = ["last_7_days", "last_30_days", "last_90_days", "custom"];
      if (!validDateRanges.includes(date_range)) {
        return res.status(400).json({
          status: false,
          message: "Invalid date_range. Must be one of: " + validDateRanges.join(", ")
        });
      }
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page) : undefined;
    const limitNumber = limit ? parseInt(limit) : undefined;

    // Validate pagination parameters
    if (pageNumber && pageNumber < 1) {
      return res.status(400).json({
        status: false,
        message: "Page number must be greater than 0"
      });
    }

    if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
      return res.status(400).json({
        status: false,
        message: "Limit must be between 1 and 100"
      });
    }

    const result = await analyticsService.getContactSubmissionAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      pageNumber,
      limitNumber
    );

    // Filter by recipe if specified (only when no pagination to avoid incorrect counts)
    let filteredData = result.data;
    let filteredTotal = result.total;

    if (recipe_id && !pageNumber && !limitNumber) {
      filteredData = result.data.filter(
        (item: any) => item.recipe_id === Number(recipe_id)
      );
      filteredTotal = filteredData.length;
    }

    const response: any = {
      status: true,
      message: res.__("CONTACT_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: filteredData,
      meta: {
        date_range: date_range || null,
        start_date: start_date || null,
        end_date: end_date || null,
        recipe_filter: recipe_id || null,
        total_records: filteredTotal,
      },
    };

    // Add pagination info if pagination was used
    if (result.pagination) {
      response.pagination = result.pagination;
    }

    return res.status(StatusCodes.OK).json(response);
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error fetching contact analytics");
  }
};

/**
 * Get recipe view analytics
 * @route GET /api/v1/private/analytics/recipe-views
 */
const getRecipeViewAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range = "last_30_days",
      start_date,
      end_date,
      sort = "desc",
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    const analytics = await analyticsService.getRecipeViewAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date
    );

    // Sort results by total views or recent views
    if (sort === "asc") {
      analytics.sort((a: any, b: any) => a.total_views - b.total_views);
    } else {
      analytics.sort((a: any, b: any) => b.total_views - a.total_views);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_VIEW_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: analytics,
      meta: {
        date_range,
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: analytics.length,
        sort_order: sort,
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error fetching recipe view analytics");
  }
};

/**
 * Delete contact form submission
 * @route DELETE /api/v1/private/analytics/contact-submissions/:id
 */
const deleteContactSubmission = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;

    // Validate ID parameter
    if (!id || isNaN(Number(id))) {
      return res.status(400).json({
        status: false,
        message: "Valid submission ID is required"
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(req.user);

    // Build where clause
    const whereClause: any = {
      id: Number(id),
      event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
    };

    // Add organization filter if not admin
    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    const deleted = await Analytics.destroy({
      where: whereClause,
    });

    if (!deleted) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CONTACT_SUBMISSION_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONTACT_SUBMISSION_DELETED_SUCCESSFULLY"),
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error deleting contact submission");
  }
};

/**
 * Get analytics summary - Real data from database
 * @route GET /api/v1/private/analytics
 */
const getAnalyticsSummary = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      page,
      limit,
      event_type,
      entity_type,
      entity_id,
      start_date,
      end_date,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    // Get real analytics data from service
    const summary = await analyticsService.getAnalyticsSummary({
      organizationId: effectiveOrganizationId || undefined,
      page: parseInt(page) || 1,
      limit: Math.min(parseInt(limit) || 10, 50),
      event_type,
      entity_type,
      entity_id,
      start_date,
      end_date,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Analytics summary fetched successfully",
      data: summary,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error fetching analytics summary");
  }
};

export default {
  // Public analytics (simplified)
  trackCtaClick,
  submitContactForm,
  trackRecipeView,

  // Dashboard analytics (simplified)
  getCtaClickAnalytics, 
  getContactSubmissionAnalytics,
  getRecipeViewAnalytics,
  deleteContactSubmission,
  getAnalyticsSummary,
};
