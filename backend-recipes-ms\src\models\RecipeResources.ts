import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeResourceType {
  item = "item",
  link = "link",
}

export enum RecipeResourceLinkType {
  image = "image",
  video = "video",
  pdf = "pdf",
  audio = "audio",
  youtube = "youtube",
  link = "link",
  document = "document",
  other = "other",
  text = "text",
}

export enum RecipeResourceStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeResourcesAttributes {
  id?: number;
  recipe_id: number;
  type: RecipeResourceType;
  item_id?: number;
  item_link?: string;
  item_link_type?: RecipeResourceLinkType;
  status: RecipeResourceStatus;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeResources
  extends Model<RecipeResourcesAttributes, never>
  implements RecipeResourcesAttributes {
  id!: number;
  recipe_id!: number;
  type!: RecipeResourceType;
  item_id?: number;
  item_link?: string;
  item_link_type?: RecipeResourceLinkType;
  status!: RecipeResourceStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeResources.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    type: {
      type: DataTypes.ENUM(Object.values(RecipeResourceType)),
      allowNull: false,
    },
    item_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    item_link: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    item_link_type: {
      type: DataTypes.ENUM(Object.values(RecipeResourceLinkType)),
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeResourceStatus)),
      allowNull: false,
      defaultValue: RecipeResourceStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_resources",
    modelName: "RecipeResources",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        fields: ["recipe_id"],
        name: "idx_recipe_resources_recipe",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_resources_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_resources_status",
      },
      {
        fields: ["type"],
        name: "idx_recipe_resources_type",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_resources_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_resources_updated_by",
      },
    ],
  }
);

// Define associations
RecipeResources.associate = (models: any) => {
  // RecipeResources belongs to Recipe
  RecipeResources.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeResources belongs to User (created_by)
  RecipeResources.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeResources belongs to User (updated_by)
  RecipeResources.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  // RecipeResources belongs to Item (item_id) - for file resources only
  // Note: No foreign key constraint because item_id can be NULL for link resources
  RecipeResources.belongsTo(models.Item, {
    foreignKey: "item_id",
    as: "resourceItem",
    constraints: false, // Disable constraint to allow NULL for link resources
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  });
};

export default RecipeResources;
