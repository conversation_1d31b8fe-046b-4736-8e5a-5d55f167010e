import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";

interface AnalyticsQuery {
  eventType?: AnalyticsEventType;
  entityType?: AnalyticsEntityType;
  entityId?: number;
  organizationId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

interface DashboardStats {
  // Core Business Metrics (matching dashboard cards)
  totalRecipes: number;
  activeUsers: number;
  topCategory: {
    name: string;
    count: number;
  };
  monthlyRevenue: number;
  growthRate: number;
  engagementRate: number;

  // Recipe Analytics
  totalViews: number;
  totalBookmarks: number;
  totalShares: number;
  totalContactSubmissions: number;

  // Dashboard Charts Data
  recipeViewsTrend: any[]; // Line chart data
  categoryPerformance: any[]; // Bar chart data
  userEngagementHeatmap: any[]; // Heatmap data
  conversionFunnel: any[]; // Conversion analytics data

  // Activity Data
  recentActivity: any[];
}

class AnalyticsService {
  /**
   * Track any event with flexible metadata
   */
  async trackEvent(data: {
    eventType: AnalyticsEventType;
    entityType: AnalyticsEntityType;
    entityId?: number;
    organizationId?: string;
    userId?: number;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    return await Analytics.trackEvent({
      event_type: data.eventType,
      entity_type: data.entityType,
      entity_id: data.entityId,
      organization_id: data.organizationId,
      user_id: data.userId,
      session_id: data.sessionId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: data.metadata,
    });
  }

  /**
   * Get analytics data with flexible filtering
   */
  async getAnalytics(query: AnalyticsQuery): Promise<Analytics[]> {
    const whereClause: any = {};

    if (query.eventType) whereClause.event_type = query.eventType;
    if (query.entityType) whereClause.entity_type = query.entityType;
    if (query.entityId) whereClause.entity_id = query.entityId;
    if (query.organizationId)
      whereClause.organization_id = query.organizationId;

    if (query.startDate || query.endDate) {
      whereClause.created_at = {};
      if (query.startDate) whereClause.created_at[Op.gte] = query.startDate;
      if (query.endDate) whereClause.created_at[Op.lte] = query.endDate;
    }

    return await Analytics.findAll({
      where: whereClause,
      order: [["created_at", "DESC"]],
      limit: query.limit || 100,
      offset: query.offset || 0,
    });
  }

  /**
   * Get comprehensive dashboard statistics - UPDATED VERSION MATCHING UI DESIGN
   */
  async getDashboardStats(
    organizationId?: string,
    dateRange: string = "last_30_days"
  ): Promise<DashboardStats> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Get all counts in parallel for new dashboard design
      const [
        recipeCount,
        activeUsersCount,
        topCategoryData,
        viewsData,
        bookmarksCount,
        sharesCount,
        contactCount,
        recipeViewsTrend,
        categoryPerformance,
        userEngagementHeatmap,
        conversionFunnel,
        recentActivity,
      ] = await Promise.all([
        this.safeGetRecipeCount(organizationId),
        this.safeGetActiveUsersCount(organizationId, startDate, endDate),
        this.safeGetTopCategory(organizationId, startDate, endDate),
        this.safeGetTotalRecipeViews(organizationId, dateRange),
        this.safeGetAnalyticsCount(
          AnalyticsEventType.RECIPE_BOOKMARK,
          organizationId,
          startDate,
          endDate
        ),
        this.safeGetAnalyticsCount(
          AnalyticsEventType.RECIPE_SHARE,
          organizationId,
          startDate,
          endDate
        ),
        this.safeGetAnalyticsCount(
          AnalyticsEventType.CONTACT_FORM_SUBMIT,
          organizationId,
          startDate,
          endDate
        ),
        this.safeGetRecipeViewsTrend(organizationId, startDate, endDate),
        this.safeGetCategoryPerformance(organizationId, startDate, endDate),
        this.safeGetUserEngagementHeatmap(organizationId, startDate, endDate),
        this.safeGetConversionFunnel(organizationId, startDate, endDate),
        this.safeGetRecentActivity(organizationId, 10),
      ]);

      // Calculate derived metrics - FIXED ENGAGEMENT RATE
      const totalViews = viewsData.totalViews || 1;
      const totalEngagements = bookmarksCount + sharesCount + contactCount;
      const engagementRate =
        totalViews > 0 ? (totalEngagements / totalViews) * 100 : 0;
      const growthRate = await this.calculateGrowthRate(
        organizationId,
        dateRange
      );
      const monthlyRevenue = await this.calculateMonthlyRevenue(
        organizationId,
        startDate,
        endDate
      );

      return {
        // Core Business Metrics (dashboard cards)
        totalRecipes: recipeCount,
        activeUsers: activeUsersCount,
        topCategory: topCategoryData,
        monthlyRevenue: monthlyRevenue,
        growthRate: growthRate,
        engagementRate: Math.round(engagementRate * 100) / 100,

        // Recipe Analytics
        totalViews: viewsData.totalViews,
        totalBookmarks: bookmarksCount,
        totalShares: sharesCount,
        totalContactSubmissions: contactCount,

        // Dashboard Charts Data
        recipeViewsTrend: recipeViewsTrend,
        categoryPerformance: categoryPerformance,
        userEngagementHeatmap: userEngagementHeatmap,
        conversionFunnel: conversionFunnel,

        // Activity Data
        recentActivity: recentActivity,
      };
    } catch (error) {
      console.error("Dashboard stats error:", error);
      // Return safe defaults matching new interface
      return {
        totalRecipes: 0,
        activeUsers: 0,
        topCategory: { name: "No Data", count: 0 },
        monthlyRevenue: 0,
        growthRate: 0,
        engagementRate: 0,
        totalViews: 0,
        totalBookmarks: 0,
        totalShares: 0,
        totalContactSubmissions: 0,
        recipeViewsTrend: [],
        categoryPerformance: [],
        userEngagementHeatmap: [],
        conversionFunnel: [],
        recentActivity: [],
      };
    }
  }

  /**
   * Get contact form submissions analytics with custom date support and pagination
   */
  async getContactSubmissionAnalytics(
    organizationId?: string,
    dateRange: string = "last_30_days",
    customStartDate?: string,
    customEndDate?: string,
    page?: number,
    limit?: number
  ): Promise<{ data: any[], total: number, pagination?: any }> {
    try {
      const { startDate, endDate } = this.getDateRange(
        dateRange,
        customStartDate,
        customEndDate
      );

      // Count query for total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM mo_recipe_analytics
        WHERE event_type = :eventType
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          AND created_at BETWEEN :startDate AND :endDate
      `;

      // Data query with optional pagination
      let dataQuery = `
        SELECT
          entity_id as recipe_id,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.contact_name')) as contact_name,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.contact_email')) as contact_email,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.contact_mobile')) as contact_mobile,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.message')) as message,
          created_at as submitted_on
        FROM mo_recipe_analytics
        WHERE event_type = :eventType
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          AND created_at BETWEEN :startDate AND :endDate
        ORDER BY created_at DESC
      `;

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        dataQuery += ` LIMIT ${limit} OFFSET ${offset}`;
      } else {
        // Default limit if no pagination
        dataQuery += ` LIMIT 100`;
      }

      const replacements = {
        eventType: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        organizationId,
        startDate,
        endDate,
      };

      // Execute both queries
      const [countResult, dataResult] = await Promise.all([
        sequelize.query(countQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
        sequelize.query(dataQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
      ]);

      const total = parseInt((countResult[0] as any)?.total || "0");
      const data = (dataResult as any[]).map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        contact_name: row.contact_name || "Unknown",
        contact_email: row.contact_email || "Unknown",
        contact_mobile: row.contact_mobile || null,
        message: row.message || "",
        submitted_on: row.submitted_on,
      }));

      // Build response with optional pagination info
      const response: { data: any[], total: number, pagination?: any } = {
        data,
        total,
      };

      if (page && limit) {
        const totalPages = Math.ceil(total / limit);
        response.pagination = {
          current_page: page,
          page_size: limit,
          total_records: total,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };
      }

      return response;
    } catch (error) {
      console.error("Contact analytics error:", error);
      return { data: [], total: 0 };
    }
  }

  // Private helper methods
  private async getRecipeStats(organizationId?: string): Promise<any> {
    // This would query your actual recipe table when it exists
    // For now, return mock data
    return { total: 0, public: 0 };
  }

  private async getIngredientStats(organizationId?: string): Promise<any> {
    try {
      const query = `
        SELECT COUNT(*) as total
        FROM mo_ingredients
        WHERE ingredient_status = 'active'
        ${organizationId ? "AND organization_id = :organizationId" : ""}
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      });

      return result[0] || { total: 0 };
    } catch (error) {
      return { total: 0 };
    }
  }

  private async getCategoryStats(organizationId?: string): Promise<any> {
    try {
      const query = `
        SELECT COUNT(*) as total
        FROM mo_category
        WHERE category_status = 'active'
        ${organizationId ? "AND organization_id = :organizationId" : ""}
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      });

      return result[0] || { total: 0 };
    } catch (error) {
      return { total: 0 };
    }
  }

  private async getViewStats(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    const result = await Analytics.count({
      where: {
        event_type: AnalyticsEventType.RECIPE_VIEW,
        ...(organizationId && { organization_id: organizationId }),
        ...(startDate &&
          endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
      },
    });

    return { total: result };
  }

  private async getCtaStats(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    const result = await Analytics.count({
      where: {
        event_type: AnalyticsEventType.CTA_CLICK,
        ...(organizationId && { organization_id: organizationId }),
        ...(startDate &&
          endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
      },
    });

    return { total: result };
  }

  private async getContactStats(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    const result = await Analytics.count({
      where: {
        event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        ...(organizationId && { organization_id: organizationId }),
        ...(startDate &&
          endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
      },
    });

    return { total: result };
  }

  private async getTopViewedRecipes(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // Use recipe_impression field for total views, with analytics for recent activity
      const query = `
        SELECT
          r.id as recipe_id,
          r.recipe_title as recipe_name,
          r.recipe_impression as total_views,
          COALESCE(recent_analytics.recent_views, 0) as recent_views
        FROM mo_recipe r
        LEFT JOIN (
          SELECT
            entity_id,
            COUNT(*) as recent_views
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
          GROUP BY entity_id
        ) recent_analytics ON r.id = recent_analytics.entity_id
        WHERE r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
          AND r.recipe_impression > 0
        ORDER BY r.recipe_impression DESC, recent_analytics.recent_views DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements: {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          organizationId,
          startDate,
          endDate,
        },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
      }));
    } catch (error: any) {
      return [];
    }
  }

  private async getTopClickedRecipes(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    const query = `
      SELECT
        entity_id as recipe_id,
        JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
        COUNT(*) as clicks
      FROM mo_recipe_analytics
      WHERE event_type = :eventType
        ${organizationId ? "AND organization_id = :organizationId" : ""}
        ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
      GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name'))
      ORDER BY clicks DESC
      LIMIT 10
    `;

    return await sequelize.query(query, {
      replacements: {
        eventType: AnalyticsEventType.CTA_CLICK,
        organizationId,
        startDate,
        endDate,
      },
      type: QueryTypes.SELECT,
    });
  }

  private async getRecentActivity(
    organizationId?: string,
    limit: number = 10
  ): Promise<any[]> {
    return await Analytics.findAll({
      where: {
        ...(organizationId && { organization_id: organizationId }),
      },
      order: [["created_at", "DESC"]],
      limit,
    });
  }

  private getDateRange(
    range: string,
    customStartDate?: string,
    customEndDate?: string
  ): { startDate: Date; endDate: Date } {
    // Handle custom date range
    if (range === "custom" && customStartDate && customEndDate) {
      return {
        startDate: new Date(customStartDate),
        endDate: new Date(customEndDate),
      };
    }

    // Handle predefined ranges
    const endDate = new Date();
    const startDate = new Date();

    switch (range) {
      case "last_7_days":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "last_30_days":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "last_90_days":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "last_year":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    return { startDate, endDate };
  }

  // ROBUST SAFE METHODS - Never throw errors, always return valid data
  private async safeGetIngredientCount(
    organizationId?: string
  ): Promise<number> {
    try {
      // Try with ingredient_status first (temporarily without org filter for testing)
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_ingredients
         WHERE ingredient_status = 'active'`,
        {
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      // Also test with org filter
      if (organizationId) {
        const orgResult = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_ingredients
           WHERE ingredient_status = 'active' AND organization_id = :organizationId`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        const orgCount = (orgResult[0] as any)?.total || 0;
      }

      return count;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_ingredients
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        const count = (result[0] as any)?.total || 0;

        return count;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  private async safeGetCategoryCount(organizationId?: string): Promise<number> {
    try {
      // Try with category_status first
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_category
         WHERE category_status = 'active'
         ${organizationId ? "AND organization_id = :organizationId" : ""}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      return (result[0] as any)?.total || 0;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_category
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  private async safeGetFoodAttributeCount(
    organizationId?: string
  ): Promise<number> {
    try {
      // Use correct column name: attribute_status (temporarily without org filter)
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_food_attributes
         WHERE attribute_status = 'active'`,
        {
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_food_attributes
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  private async safeGetRecipeMeasureCount(
    organizationId?: string
  ): Promise<number> {
    try {
      // Use correct column name: status (temporarily without org filter)
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe_measure
         WHERE status = 'active'`,
        {
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      try {
        // Fallback: try without status filter
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_recipe_measure
           ${organizationId ? "WHERE organization_id = :organizationId" : ""}`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } catch (fallbackError: any) {
        return 0;
      }
    }
  }

  /**
   * PROFESSIONAL METHOD: Get total recipe views combining all data sources
   */
  private async safeGetTotalRecipeViews(
    organizationId?: string,
    dateRange: string = "last_30_days"
  ): Promise<{ totalViews: number; breakdown: any }> {
    try {
      // Get total impressions from recipe table (all-time data)
      const recipeImpressionsQuery = `
        SELECT
          COALESCE(SUM(recipe_impression), 0) as total_impressions,
          COUNT(CASE WHEN recipe_impression > 0 THEN 1 END) as recipes_with_views
        FROM mo_recipe
        WHERE recipe_status != 'deleted'
          ${organizationId ? "AND organization_id = :organizationId" : ""}
      `;

      const impressionResult = await sequelize.query(recipeImpressionsQuery, {
        replacements: { organizationId },
        type: QueryTypes.SELECT,
      });

      const totalImpressions =
        parseInt((impressionResult[0] as any)?.total_impressions) || 0;
      const recipesWithViews =
        parseInt((impressionResult[0] as any)?.recipes_with_views) || 0;

      // Get analytics events count for the date range
      const { startDate, endDate } = this.getDateRange(dateRange);
      const analyticsResult = await Analytics.count({
        where: {
          event_type: AnalyticsEventType.RECIPE_VIEW,
          ...(organizationId && { organization_id: organizationId }),
          created_at: {
            [Op.between]: [startDate, endDate],
          },
        },
      });

      return {
        totalViews: totalImpressions + (analyticsResult || 0),
        breakdown: {
          existing_impressions: totalImpressions,
          recent_analytics: analyticsResult || 0,
          recipes_with_views: recipesWithViews,
        },
      };
    } catch (error: any) {
      console.error("Error getting total recipe views:", error);
      return {
        totalViews: 0,
        breakdown: {
          existing_impressions: 0,
          recent_analytics: 0,
          recipes_with_views: 0,
        },
      };
    }
  }

  /**
   * PROFESSIONAL METHOD: Get top viewed recipes with comprehensive data
   */
  private async safeGetTopViewedRecipes(
    organizationId?: string,
    dateRange: string = "last_30_days"
  ): Promise<any[]> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Get top recipes combining impression counts and recent analytics
      const topRecipesQuery = `
        SELECT
          r.id as recipe_id,
          r.recipe_title as recipe_name,
          r.recipe_impression as total_views,
          r.updated_at as last_updated,
          COALESCE(analytics_data.recent_views, 0) as recent_views,
          analytics_data.last_viewed,
          analytics_data.unique_sessions
        FROM mo_recipe r
        LEFT JOIN (
          SELECT
            entity_id,
            COUNT(*) as recent_views,
            MAX(created_at) as last_viewed,
            COUNT(DISTINCT session_id) as unique_sessions
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            AND created_at BETWEEN :startDate AND :endDate
          GROUP BY entity_id
        ) analytics_data ON r.id = analytics_data.entity_id
        WHERE r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
          AND (r.recipe_impression > 0 OR analytics_data.recent_views > 0)
        ORDER BY
          (r.recipe_impression + COALESCE(analytics_data.recent_views, 0)) DESC,
          r.recipe_impression DESC
        LIMIT 10
      `;

      const result = await sequelize.query(topRecipesQuery, {
        replacements: {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          organizationId,
          startDate,
          endDate,
        },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
        total_combined_views:
          (parseInt(row.total_views) || 0) + (parseInt(row.recent_views) || 0),
        last_viewed: row.last_viewed,
        last_updated: row.last_updated,
        unique_sessions: parseInt(row.unique_sessions) || 0,
      }));
    } catch (error: any) {
      console.error("Error getting top viewed recipes:", error);
      return [];
    }
  }

  private async safeGetAnalyticsCount(
    eventType: AnalyticsEventType,
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      // For non-RECIPE_VIEW events, use analytics table only
      const result = await Analytics.count({
        where: {
          event_type: eventType,
          ...(organizationId && { organization_id: organizationId }),
          ...(startDate &&
            endDate && {
              created_at: {
                [Op.between]: [startDate, endDate],
              },
            }),
        },
      });
      return result || 0;
    } catch (error: any) {
      console.error(`Error getting analytics count for ${eventType}:`, error);
      return 0;
    }
  }

  private async safeGetTopRecipes(
    eventType: AnalyticsEventType,
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // PERFECT SOLUTION: Include ALL non-aggregated columns in GROUP BY
      const result = await sequelize.query(
        `SELECT
           entity_id as recipe_id,
           JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
           COUNT(*) as count
         FROM mo_recipe_analytics
         WHERE event_type = :eventType
           ${organizationId ? "AND organization_id = :organizationId" : ""}
           ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
         GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name'))
         ORDER BY count DESC
         LIMIT 5`,
        {
          replacements: { eventType, organizationId, startDate, endDate },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        count: parseInt(row.count) || 0,
      }));
    } catch (error: any) {
      console.error(`Error getting top recipes for ${eventType}:`, error);
      return [];
    }
  }

  private async safeGetRecentActivity(
    organizationId?: string,
    limit: number = 10
  ): Promise<any[]> {
    try {
      const result = await Analytics.findAll({
        where: {
          ...(organizationId && { organization_id: organizationId }),
        },
        order: [["created_at", "DESC"]],
        limit,
        attributes: [
          "event_type",
          "entity_type",
          "entity_id",
          "metadata",
          "created_at",
        ],
      });
      return result || [];
    } catch {
      return [];
    }
  }

  /**
   * Safely get total recipe count
   */
  private async safeGetRecipeCount(organizationId?: string): Promise<number> {
    try {
      // First, let's check if the table exists and what data is there
      const tableCheck = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe`,
        { type: QueryTypes.SELECT }
      );

      // Check recipes for this organization
      const orgResult = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe
         WHERE recipe_status != 'deleted'
         ${organizationId ? "AND organization_id = :organizationId" : ""}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );

      // Also check what organization IDs exist
      if (organizationId) {
        const orgCheck = await sequelize.query(
          `SELECT DISTINCT organization_id FROM mo_recipe LIMIT 5`,
          { type: QueryTypes.SELECT }
        );
      }

      const count = (orgResult[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      return 0;
    }
  }

  /**
   * Safely get public recipe count
   */
  private async safeGetPublicRecipeCount(
    organizationId?: string
  ): Promise<number> {
    try {
      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe
         WHERE recipe_status = 'publish'
         AND has_recipe_public_visibility = true
         ${organizationId ? "AND organization_id = :organizationId" : ""}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      const count = (result[0] as any)?.total || 0;

      return count;
    } catch (error: any) {
      return 0;
    }
  }

  /**
   * Get CTA click analytics with custom date support and pagination
   */
  async getCtaClickAnalytics(
    organizationId?: string,
    dateRange: string = "last_30_days",
    customStartDate?: string,
    customEndDate?: string,
    page?: number,
    limit?: number
  ): Promise<{ data: any[], total: number, pagination?: any }> {
    try {
      const { startDate, endDate } = this.getDateRange(
        dateRange,
        customStartDate,
        customEndDate
      );

      // Base query for counting total records
      const countQuery = `
        SELECT COUNT(*) as total
        FROM (
          SELECT
            entity_id,
            JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
            JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type')) as cta_type
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            AND created_at BETWEEN :startDate AND :endDate
          GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')), JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type'))
        ) as grouped_data
      `;

      // Data query with optional pagination
      let dataQuery = `
        SELECT
          entity_id as recipe_id,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')) as recipe_name,
          JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type')) as cta_type,
          COUNT(*) as clicks,
          MAX(created_at) as last_clicked_at
        FROM mo_recipe_analytics
        WHERE event_type = :eventType
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          AND created_at BETWEEN :startDate AND :endDate
        GROUP BY entity_id, JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.recipe_name')), JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.cta_type'))
        ORDER BY clicks DESC
      `;

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        dataQuery += ` LIMIT ${limit} OFFSET ${offset}`;
      } else {
        // Default limit if no pagination
        dataQuery += ` LIMIT 50`;
      }

      const replacements = {
        eventType: AnalyticsEventType.CTA_CLICK,
        organizationId,
        startDate,
        endDate,
      };

      // Execute both queries
      const [countResult, dataResult] = await Promise.all([
        sequelize.query(countQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
        sequelize.query(dataQuery, {
          replacements,
          type: QueryTypes.SELECT,
        }),
      ]);

      const total = parseInt((countResult[0] as any)?.total || "0");
      const data = (dataResult as any[]).map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        cta_type: row.cta_type || "unknown",
        clicks: parseInt(row.clicks) || 0,
        last_clicked_at: row.last_clicked_at,
      }));

      // Build response with optional pagination info
      const response: { data: any[], total: number, pagination?: any } = {
        data,
        total,
      };

      if (page && limit) {
        const totalPages = Math.ceil(total / limit);
        response.pagination = {
          current_page: page,
          page_size: limit,
          total_records: total,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };
      }

      return response;
    } catch (error: any) {
      console.error("Error in getCtaClickAnalytics:", error);
      return { data: [], total: 0 };
    }
  }

  /**
   * PROFESSIONAL METHOD: Get comprehensive recipe view analytics
   * Combines existing impressions with detailed analytics metadata
   */
  async getRecipeViewAnalytics(
    organizationId?: string,
    dateRange: string = "last_30_days",
    customStartDate?: string,
    customEndDate?: string
  ): Promise<any[]> {
    try {
      const { startDate, endDate } = this.getDateRange(
        dateRange,
        customStartDate,
        customEndDate
      );

      // Get comprehensive recipe analytics combining all data sources
      const query = `
        SELECT
          r.id as recipe_id,
          r.recipe_title as recipe_name,
          r.recipe_impression as total_views,
          r.updated_at as last_updated,
          r.recipe_status,
          r.has_recipe_public_visibility,
          COALESCE(analytics_data.recent_views, 0) as recent_views,
          analytics_data.last_viewed,
          analytics_data.avg_view_duration,
          analytics_data.unique_sessions,
          analytics_data.total_analytics_views,
          (r.recipe_impression + COALESCE(analytics_data.total_analytics_views, 0)) as combined_total_views
        FROM mo_recipe r
        LEFT JOIN (
          SELECT
            entity_id,
            COUNT(*) as recent_views,
            COUNT(*) as total_analytics_views,
            MAX(created_at) as last_viewed,
            AVG(CAST(JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.view_duration')) AS UNSIGNED)) as avg_view_duration,
            COUNT(DISTINCT session_id) as unique_sessions
          FROM mo_recipe_analytics
          WHERE event_type = :eventType
            ${organizationId ? "AND organization_id = :organizationId" : ""}
            ${customStartDate && customEndDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
          GROUP BY entity_id
        ) analytics_data ON r.id = analytics_data.entity_id
        WHERE r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
          AND (r.recipe_impression > 0 OR analytics_data.recent_views > 0)
        ORDER BY
          combined_total_views DESC,
          r.recipe_impression DESC,
          analytics_data.recent_views DESC
        LIMIT 50
      `;

      const result = await sequelize.query(query, {
        replacements: {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          organizationId,
          startDate,
          endDate,
        },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        recipe_id: row.recipe_id,
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`,
        total_views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
        combined_total_views: parseInt(row.combined_total_views) || 0,
        last_viewed: row.last_viewed,
        last_updated: row.last_updated,
        avg_view_duration: parseInt(row.avg_view_duration) || 0,
        unique_sessions: parseInt(row.unique_sessions) || 0,
        recipe_status: row.recipe_status,
        is_public: row.has_recipe_public_visibility,
      }));
    } catch (error: any) {
      console.error("Error getting recipe view analytics:", error);
      return [];
    }
  }

  /**
   * Get analytics summary with real data from database
   */
  async getAnalyticsSummary(filters: {
    organizationId?: string;
    page?: number;
    limit?: number;
    event_type?: string;
    entity_type?: string;
    entity_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<any> {
    try {
      const {
        organizationId,
        page = 1,
        limit = 20,
        event_type,
        entity_type,
        entity_id,
        start_date,
        end_date,
      } = filters;

      // Build where conditions
      const whereConditions: any = {};

      if (organizationId) {
        whereConditions.organization_id = organizationId;
      }

      if (event_type) {
        whereConditions.event_type = event_type;
      }

      if (entity_type) {
        whereConditions.entity_type = entity_type;
      }

      if (entity_id) {
        whereConditions.entity_id = entity_id;
      }

      // Date filtering
      if (start_date && end_date) {
        whereConditions.created_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)],
        };
      } else {
        // Default to last 30 days if no date range specified
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereConditions.created_at = {
          [Op.gte]: thirtyDaysAgo,
        };
      }

      // Get total counts by event type
      const eventTypeCounts = await Analytics.findAll({
        attributes: [
          "event_type",
          [sequelize.fn("COUNT", sequelize.col("id")), "count"],
        ],
        where: whereConditions,
        group: ["event_type"],
        raw: true,
      });

      // Get recent events with pagination
      const offset = (page - 1) * limit;
      const { count, rows } = await Analytics.findAndCountAll({
        where: whereConditions,
        limit: limit,
        offset: offset,
        order: [["created_at", "DESC"]],
        attributes: [
          "id",
          "event_type",
          "entity_type",
          "entity_id",
          "metadata",
          "created_at",
        ],
      });

      // Format event type counts
      const eventTypes = {
        recipe_view: 0,
        cta_click: 0,
        contact_form_submit: 0,
      };

      eventTypeCounts.forEach((item: any) => {
        if (item.event_type in eventTypes) {
          eventTypes[item.event_type as keyof typeof eventTypes] = parseInt(
            item.count
          );
        }
      });

      const totalEvents = Object.values(eventTypes).reduce(
        (sum, count) => sum + count,
        0
      );

      return {
        total_events: totalEvents,
        recent_events: rows.map((event: any) => ({
          id: event.id,
          event_type: event.event_type,
          entity_type: event.entity_type,
          entity_id: event.entity_id,
          metadata: event.metadata,
          created_at: event.created_at,
        })),
        event_types: eventTypes,
        date_range: {
          start_date: start_date || null,
          end_date: end_date || null,
        },
        pagination: {
          page: page,
          limit: limit,
          total: count,
          totalPages: Math.ceil(count / limit),
        },
      };
    } catch (error: any) {
      // Return safe defaults on error
      return {
        total_events: 0,
        recent_events: [],
        event_types: {
          recipe_view: 0,
          cta_click: 0,
          contact_form_submit: 0,
        },
        date_range: {
          start_date: filters.start_date || null,
          end_date: filters.end_date || null,
        },
        pagination: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          total: 0,
          totalPages: 0,
        },
        error: "Failed to fetch analytics data",
      };
    }
  }

  // ============================================================================
  // NEW DASHBOARD METHODS - MATCHING UI DESIGN
  // ============================================================================

  /**
   * Get active users count (users who performed any action in date range)
   */
  private async safeGetActiveUsersCount(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      const result = await Analytics.count({
        distinct: true,
        col: "user_id",
        where: {
          ...(organizationId && { organization_id: organizationId }),
          user_id: { [Op.not]: null },
          ...(startDate &&
            endDate && {
              created_at: {
                [Op.between]: [startDate, endDate],
              },
            }),
        },
      });
      return result || 0;
    } catch (error) {
      console.error("Error getting active users count:", error);
      return 0;
    }
  }

  /**
   * Get top performing category
   */
  private async safeGetTopCategory(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{ name: string; count: number }> {
    try {
      // Create fallback categories based on recipe IDs in analytics
      const query = `
        SELECT 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END as name,
          COUNT(*) as count
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END
        ORDER BY count DESC
        LIMIT 1
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch (error) {
      console.error("Error getting top category:", error);
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get recipe views trend data for line chart
   */
  private async safeGetRecipeViewsTrend(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT 
          DATE(created_at) as date,
          COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as views,
          COUNT(CASE WHEN event_type = 'recipe_bookmark' THEN 1 END) as likes
        FROM mo_recipe_analytics
        WHERE 1=1
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
        LIMIT 30
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        date: row.date,
        views: parseInt(row.views) || 0,
        likes: parseInt(row.likes) || 0,
      }));
    } catch (error) {
      console.error("Error getting recipe views trend:", error);
      return [];
    }
  }

  /**
   * Get category performance data for bar chart
   */
  private async safeGetCategoryPerformance(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // Use analytics data to build category performance with fallback categories
      const query = `
        SELECT 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END as category,
          COUNT(DISTINCT a.entity_id) as recipes,
          COUNT(*) as views
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END
        HAVING COUNT(*) > 0
        ORDER BY views DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        category: row.category,
        recipes: parseInt(row.recipes) || 0,
        views: parseInt(row.views) || 0,
      }));
    } catch (error) {
      console.error("Error getting category performance:", error);
      return [];
    }
  }

  /**
   * Get user engagement heatmap data
   */
  private async safeGetUserEngagementHeatmap(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT 
          HOUR(created_at) as hour,
          DAYOFWEEK(created_at) as day_of_week,
          COUNT(*) as engagement_count
        FROM mo_recipe_analytics
        WHERE 1=1
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY HOUR(created_at), DAYOFWEEK(created_at)
        ORDER BY day_of_week, hour
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        hour: parseInt(row.hour),
        day: parseInt(row.day_of_week),
        count: parseInt(row.engagement_count) || 0,
      }));
    } catch (error) {
      console.error("Error getting user engagement heatmap:", error);
      return [];
    }
  }

  /**
   * Get conversion funnel data
   */
  private async safeGetConversionFunnel(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const [visitors, recipeViews, bookmarks, shares, subscriptions] =
        await Promise.all([
          Analytics.count({
            distinct: true,
            col: "session_id",
            where: {
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_VIEW,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_BOOKMARK,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_SHARE,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                  created_at: { [Op.between]: [startDate, endDate] },
                }),
            },
          }),
        ]);

      const maxValue =
        Math.max(visitors, recipeViews, bookmarks, shares, subscriptions) || 1;

      return [
        { stage: "Visitors", count: visitors, percentage: 100 },
        {
          stage: "Recipe Views",
          count: recipeViews,
          percentage: Math.round((recipeViews / maxValue) * 100),
        },
        {
          stage: "Bookmarks",
          count: bookmarks,
          percentage: Math.round((bookmarks / maxValue) * 100),
        },
        {
          stage: "Shares",
          count: shares,
          percentage: Math.round((shares / maxValue) * 100),
        },
        {
          stage: "Subscriptions",
          count: subscriptions,
          percentage: Math.round((subscriptions / maxValue) * 100),
        },
      ];
    } catch (error) {
      console.error("Error getting conversion funnel:", error);
      return [];
    }
  }

  /**
   * Calculate growth rate compared to previous period
   */
  private async calculateGrowthRate(
    organizationId?: string,
    dateRange: string = "last_30_days"
  ): Promise<number> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);
      const periodDuration = endDate.getTime() - startDate.getTime();
      const previousEndDate = new Date(startDate.getTime() - 1);
      const previousStartDate = new Date(
        previousEndDate.getTime() - periodDuration
      );

      const [currentViews, previousViews] = await Promise.all([
        Analytics.count({
          where: {
            event_type: AnalyticsEventType.RECIPE_VIEW,
            ...(organizationId && { organization_id: organizationId }),
            created_at: { [Op.between]: [startDate, endDate] },
          },
        }),
        Analytics.count({
          where: {
            event_type: AnalyticsEventType.RECIPE_VIEW,
            ...(organizationId && { organization_id: organizationId }),
            created_at: { [Op.between]: [previousStartDate, previousEndDate] },
          },
        }),
      ]);

      if (previousViews === 0) return currentViews > 0 ? 100 : 0;
      return (
        Math.round(
          ((currentViews - previousViews) / previousViews) * 100 * 100
        ) / 100
      );
    } catch (error) {
      console.error("Error calculating growth rate:", error);
      return 0;
    }
  }

  /**
   * Calculate monthly revenue (placeholder - implement based on your business model)
   */
  private async calculateMonthlyRevenue(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      // This is a placeholder implementation
      // You should implement this based on your actual revenue model
      // For example: subscription fees, premium features, etc.

      const contactSubmissions = await Analytics.count({
        where: {
          event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
          ...(organizationId && { organization_id: organizationId }),
          ...(startDate &&
            endDate && {
              created_at: { [Op.between]: [startDate, endDate] },
            }),
        },
      });

      // Example: Estimate $50 revenue per contact form submission
      return contactSubmissions * 50;
    } catch (error) {
      console.error("Error calculating monthly revenue:", error);
      return 0;
    }
  }

  // ============================================================================
  // END NEW DASHBOARD METHODS
  // ============================================================================
}

export default new AnalyticsService();
