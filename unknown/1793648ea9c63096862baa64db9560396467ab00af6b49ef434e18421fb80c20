import express from "express";
import analyticsController from "../../controller/analytics.controller";
import analyticsValidator from "../../validators/analytics.validator";

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     PublicAnalyticsEvent:
 *       type: object
 *       properties:
 *         event_type:
 *           type: string
 *           enum: [recipe_view, cta_click, contact_form_submit, recipe_download, recipe_share]
 *         entity_type:
 *           type: string
 *           enum: [recipe, category]
 *         entity_id:
 *           type: number
 *         organization_id:
 *           type: string
 *         session_id:
 *           type: string
 *         metadata:
 *           type: object
 *       required:
 *         - event_type
 *         - entity_type
 */

/**
 * @swagger
 * /v1/public/analytics/track:
 *   post:
 *     summary: Track a public event (no authentication required)
 *     tags: [Public Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PublicAnalyticsEvent'
 *           examples:
 *             recipe_view:
 *               summary: Track recipe view
 *               value:
 *                 event_type: "recipe_view"
 *                 entity_type: "recipe"
 *                 entity_id: 123
 *                 organization_id: "org_123"
 *                 session_id: "sess_abc123"
 *                 metadata:
 *                   recipe_name: "Chocolate Cake"
 *                   view_duration: 45
 *                   referrer: "https://google.com"
 *             cta_click:
 *               summary: Track CTA click
 *               value:
 *                 event_type: "cta_click"
 *                 entity_type: "recipe"
 *                 entity_id: 123
 *                 organization_id: "org_123"
 *                 session_id: "sess_abc123"
 *                 metadata:
 *                   recipe_name: "Chocolate Cake"
 *                   cta_type: "contact_form"
 *                   cta_text: "Get Recipe Details"
 *             contact_form_submit:
 *               summary: Track contact form submission
 *               value:
 *                 event_type: "contact_form_submit"
 *                 entity_type: "recipe"
 *                 entity_id: 123
 *                 organization_id: "org_123"
 *                 session_id: "sess_abc123"
 *                 metadata:
 *                   recipe_name: "Chocolate Cake"
 *                   contact_name: "John Doe"
 *                   contact_email: "<EMAIL>"
 *                   contact_mobile: "+1234567890"
 *                   message: "I love this recipe!"
 *     responses:
 *       201:
 *         description: Event tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Event tracked successfully"
 *       400:
 *         description: Invalid event data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Event type and entity type are required"
 *       500:
 *         description: Internal server error
 */
// REMOVED - Not needed per feature document
// Only need CTA click tracking and contact form submissions

/**
 * @swagger
 * /v1/public/analytics/track/recipe-view:
 *   post:
 *     summary: Track recipe interactions (view, bookmark, share)
 *     tags: [Public Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recipe_id:
 *                 type: number
 *                 description: Recipe ID
 *                 example: 123
 *               organization_id:
 *                 type: string
 *                 description: Organization ID
 *                 example: "org_123"
 *               session_id:
 *                 type: string
 *                 description: Session ID
 *                 example: "session_abc123"
 *               recipe_name:
 *                 type: string
 *                 description: Recipe name for metadata
 *                 example: "Chocolate Cake"
 *               event_type:
 *                 type: string
 *                 enum: [recipe_view, recipe_bookmark, recipe_share]
 *                 default: recipe_view
 *                 description: Type of interaction to track
 *               view_duration:
 *                 type: number
 *                 description: View duration in seconds (for recipe_view)
 *                 example: 45
 *               referrer:
 *                 type: string
 *                 description: Referrer URL (for recipe_view)
 *                 example: "https://google.com"
 *               share_platform:
 *                 type: string
 *                 enum: [facebook, twitter, whatsapp, email, copy_link]
 *                 description: Share platform (for recipe_share)
 *                 example: "facebook"
 *             required:
 *               - recipe_id
 *           examples:
 *             recipe_view:
 *               summary: Track recipe view
 *               value:
 *                 recipe_id: 123
 *                 organization_id: "org_123"
 *                 session_id: "session_abc123"
 *                 recipe_name: "Chocolate Cake"
 *                 event_type: "recipe_view"
 *                 view_duration: 45
 *                 referrer: "https://google.com"
 *             recipe_bookmark:
 *               summary: Track recipe bookmark
 *               value:
 *                 recipe_id: 123
 *                 organization_id: "org_123"
 *                 session_id: "session_abc123"
 *                 recipe_name: "Chocolate Cake"
 *                 event_type: "recipe_bookmark"
 *             recipe_share:
 *               summary: Track recipe share
 *               value:
 *                 recipe_id: 123
 *                 organization_id: "org_123"
 *                 session_id: "session_abc123"
 *                 recipe_name: "Chocolate Cake"
 *                 event_type: "recipe_share"
 *                 share_platform: "facebook"
 *     responses:
 *       201:
 *         description: Recipe interaction tracked successfully
 *       400:
 *         description: Recipe ID is required
 *       500:
 *         description: Internal server error
 */
router.post(
  "/track/recipe-view",
  analyticsValidator.trackRecipeViewValidator(),
  analyticsController.trackRecipeView
);

/**
 * @swagger
 * /v1/public/analytics/track/cta-click:
 *   post:
 *     summary: Track CTA click (simplified endpoint)
 *     tags: [Public Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recipe_id:
 *                 type: number
 *                 description: Recipe ID
 *               organization_id:
 *                 type: string
 *                 description: Organization ID
 *               session_id:
 *                 type: string
 *                 description: Session ID
 *               recipe_name:
 *                 type: string
 *                 description: Recipe name for metadata
 *               cta_type:
 *                 type: string
 *                 enum: [contact_info, contact_form, custom_cta]
 *                 description: Type of CTA clicked
 *               cta_text:
 *                 type: string
 *                 description: CTA button text
 *             required:
 *               - recipe_id
 *               - cta_type
 *           example:
 *             recipe_id: 123
 *             organization_id: "org_123"
 *             session_id: "sess_abc123"
 *             recipe_name: "Chocolate Cake"
 *             cta_type: "contact_form"
 *             cta_text: "Get Recipe Details"
 *     responses:
 *       201:
 *         description: CTA click tracked successfully
 *       400:
 *         description: Recipe ID and CTA type are required
 *       500:
 *         description: Internal server error
 */
/**
 * Track CTA clicks on public recipes
 * Simple endpoint matching your feature requirements
 */
router.post(
  "/track/cta-click",
  analyticsValidator.trackCtaClickValidator(),
  analyticsController.trackCtaClick
);

/**
 * @swagger
 * /v1/public/analytics/contact-form:
 *   post:
 *     summary: Submit contact form (public - no auth required)
 *     tags: [Public Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recipe_id:
 *                 type: number
 *                 description: Recipe ID
 *                 example: 123
 *               organization_id:
 *                 type: string
 *                 description: Organization ID
 *                 example: "org_123"
 *               recipe_name:
 *                 type: string
 *                 description: Recipe name for metadata
 *                 example: "Chocolate Cake"
 *               name:
 *                 type: string
 *                 description: Contact person name
 *                 example: "John Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Contact email address
 *                 example: "<EMAIL>"
 *               mobile:
 *                 type: string
 *                 description: Contact mobile number
 *                 example: "+1234567890"
 *               message:
 *                 type: string
 *                 description: Contact message
 *                 example: "I want the complete recipe for this chocolate cake!"
 *             required:
 *               - recipe_id
 *               - name
 *               - email
 *               - message
 *           example:
 *             recipe_id: 123
 *             organization_id: "org_123"
 *             name: "John Doe"
 *             email: "<EMAIL>"
 *             mobile: "+1234567890"
 *             message: "I want the complete recipe for this chocolate cake!"
 *             recipe_name: "Chocolate Cake"
 *     responses:
 *       201:
 *         description: Contact form submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Contact form submitted successfully"
 *       400:
 *         description: Required fields missing
 *       500:
 *         description: Internal server error
 */
router.post(
  "/contact-form",
  analyticsValidator.submitContactFormValidator(),
  analyticsController.submitContactForm
);

export default router;
