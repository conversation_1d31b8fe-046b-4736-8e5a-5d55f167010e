import { celebrate, Joi, Segments } from "celebrate";

/**
 * Validator for dashboard overview endpoint
 * Validates date_range query parameter
 */
const getDashboardOverviewValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .default("last_30_days")
        .description("Date range for dashboard analytics"),
    },
  });

/**
 * Validator for dashboard export endpoint
 * Validates format and date_range query parameters
 */
const exportDashboardDataValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      format: Joi.string()
        .valid("json", "csv")
        .default("json")
        .description("Export format"),
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .default("last_30_days")
        .description("Date range for exported data"),
    },
  });

/**
 * Validator for CTA analytics endpoint in dashboard
 * Reuses analytics validation logic for consistency
 */
const getCtaAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .optional(),
      sort: Joi.string().valid("asc", "desc").optional(),
    },
  });

/**
 * Validator for contact analytics endpoint in dashboard
 * Reuses analytics validation logic for consistency
 */
const getContactAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .optional(),
      recipe_id: Joi.number().optional(),
    },
  });

export default {
  getDashboardOverviewValidator,
  exportDashboardDataValidator,
  getCtaAnalyticsValidator,
  getContactAnalyticsValidator,
};
