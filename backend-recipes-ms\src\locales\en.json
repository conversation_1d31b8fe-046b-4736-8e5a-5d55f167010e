{"SOMETHING_WENT_WRONG": "Something went wrong", "SUCCESS_DATA_FETCHED": "Data fetched successfully", "CATEGORY_CREATED_SUCCESSFULLY": "Category created successfully", "CATEGORY_UPDATED_SUCCESSFULLY": "Category updated successfully", "CATEGORY_DELETED_SUCCESSFULLY": "Category deleted successfully", "CATEGORY_DEACTIVATED_SUCCESSFULLY": "Category deactivated successfully as it is being used in recipes or ingredients", "CATEGORY_REACTIVATED_SUCCESSFULLY": "Category reactivated successfully", "CATEGORY_NOT_FOUND": "Category not found", "CATEGORY_ALREADY_EXISTS": "Category already exists", "CATEGORY_IN_USE_CANNOT_DELETE": "Cannot delete category. It is being used in recipes or ingredients. Please remove all references first.", "ATTRIBUTE_CREATED_SUCCESSFULLY": "Attribute created successfully", "ATTRIBUTE_UPDATED_SUCCESSFULLY": "Attribute updated successfully", "ATTRIBUTE_DELETED_SUCCESSFULLY": "Attribute deleted successfully", "ATTRIBUTE_DEACTIVATED_SUCCESSFULLY": "Attribute deactivated successfully as it is being used in recipes or ingredients", "ATTRIBUTE_REACTIVATED_SUCCESSFULLY": "Attribute reactivated successfully", "ATTRIBUTE_NOT_FOUND": "Attribute not found", "ATTRIBUTE_ALREADY_EXISTS": "Attribute already exists", "ATTRIBUTE_IN_USE_CANNOT_DELETE": "Cannot delete attribute. It is being used in recipes or ingredients. Please remove all references first.", "UNIT_CREATED_SUCCESSFULLY": "Unit created successfully", "UNIT_UPDATED_SUCCESSFULLY": "Unit updated successfully", "UNIT_DELETED_SUCCESSFULLY": "Unit deleted successfully", "UNIT_DEACTIVATED_SUCCESSFULLY": "Unit deactivated successfully as it is being used in recipes or ingredients", "UNIT_REACTIVATED_SUCCESSFULLY": "Unit reactivated successfully", "UNIT_NOT_FOUND": "Unit not found", "UNIT_ALREADY_EXISTS": "Unit already exists", "UNIT_IN_USE_CANNOT_DELETE": "Cannot delete unit. It is being used in recipes, ingredients, or conversions. Please remove all references first.", "NO_FILE_UPLOADED": "No file uploaded", "FILES_UPLOADED_SUCCESSFULLY": "Files uploaded successfully", "FILE_UPLOAD_FAILED": "File upload failed", "INVALID_FILE_TYPE": "Invalid file type", "FILE_TOO_LARGE": "File size too large", "RECIPE_ID_REQUIRED": "Recipe ID is required", "INVALID_RECIPE_ID": "Invalid recipe ID", "INVALID_RECIPE_IDENTIFIER": "Invalid recipe identifier", "INGREDIENT_ID_REQUIRED": "Ingredient ID is required", "INVALID_INGREDIENT_ID": "Invalid ingredient ID", "INVALID_INGREDIENT_IDENTIFIER": "Invalid ingredient identifier", "CATEGORY_ID_REQUIRED": "Category ID is required", "ATTRIBUTE_ID_REQUIRED": "Attribute ID is required", "UNIT_ID_REQUIRED": "Unit ID is required", "ENTITY_ID_REQUIRED": "Entity ID is required", "CATEGORY_NAME_AND_TYPE_REQUIRED": "Category name and type are required", "ATTRIBUTE_TITLE_AND_TYPE_REQUIRED": "Attribute title and type are required", "UNIT_TITLE_REQUIRED": "Unit title is required", "INGREDIENT_CREATED_SUCCESSFULLY": "Ingredient created successfully", "INGREDIENT_UPDATED_SUCCESSFULLY": "Ingredient updated successfully", "INGREDIENT_DELETED_SUCCESSFULLY": "Ingredient deleted successfully", "INGREDIENT_DEACTIVATED_SUCCESSFULLY": "Ingredient deactivated successfully as it is being used in recipes", "INGREDIENT_REACTIVATED_SUCCESSFULLY": "Ingredient reactivated successfully", "INGREDIENT_NOT_FOUND": "Ingredient not found", "INGREDIENT_ALREADY_EXISTS": "Ingredient already exists", "INGREDIENT_IN_USE_CANNOT_DELETE": "Cannot delete ingredient. It is being used in recipes. Please remove all references first.", "INGREDIENTS_FETCHED_SUCCESSFULLY": "Ingredients fetched successfully", "INGREDIENT_FETCHED_SUCCESSFULLY": "Ingredient fetched successfully", "INGREDIENTS_SEARCHED_SUCCESSFULLY": "Ingredients searched successfully", "ERROR_CREATING_INGREDIENT": "Error creating ingredient", "ERROR_FETCHING_INGREDIENTS": "Error fetching ingredients", "ERROR_FETCHING_INGREDIENT": "Error fetching ingredient", "ERROR_UPDATING_INGREDIENT": "Error updating ingredient", "ERROR_DELETING_INGREDIENT": "Error deleting ingredient", "ERROR_SEARCHING_INGREDIENTS": "Error searching ingredients", "ORGANIZATION_ID_REQUIRED": "Organization ID is required", "INVALID_EXCEL_FILE": "Invalid Excel file", "MISSING_REQUIRED_HEADERS": "Missing required headers in Excel file", "INGREDIENTS_IMPORTED_SUCCESSFULLY": "Ingredients imported successfully", "ERROR_IMPORTING_INGREDIENTS": "Error importing ingredients", "ERROR_GENERATING_TEMPLATE": "Error generating Excel template", "PARTIAL_IMPORT_WARNING": "Some data could not be imported due to missing references", "ERROR_EXPORTING_INGREDIENTS": "Error exporting ingredients", "ERROR_INVALID_TOKEN": "Token is invalid or expired", "CATEGORY_NAME_ALREADY_EXISTS": "Category name already exists", "ATTRIBUTE_TITLE_ALREADY_EXISTS": "Attribute title already exists", "UNIT_TITLE_ALREADY_EXISTS": "Unit title already exists", "CANNOT_DELETE_SYSTEM_CATEGORY": "Cannot delete system category", "CANNOT_DELETE_SYSTEM_ATTRIBUTE": "Cannot delete system attribute", "CATEGORY_NAME_TOO_SHORT": "Category name must be at least 2 characters long", "UNAUTHORIZED_SUPER_ADMIN": "Only authorized Super Admin can perform this operation", "INVALID_SUPER_ADMIN_EMAIL": "Invalid Super Admin email address", "CANNOT_DELETE_SYSTEM_UNIT": "Cannot delete system unit", "ERROR_TOKEN_REQUIRED": "To<PERSON> not found.", "CONTACT_US_CREATED_SUCCESSFULLY": "Contact us submission created successfully", "CONTACT_US_UPDATED_SUCCESSFULLY": "Contact us submission updated successfully", "CONTACT_US_DELETED_SUCCESSFULLY": "Contact us submission deleted successfully", "CONTACT_US_NOT_FOUND": "Contact us submission not found", "INGREDIENT_RETRIEVED_SUCCESSFULLY": "Ingredient retrieved successfully", "ERROR_CREATING_RECIPE": "Error creating recipe", "RECIPE_CREATED_SUCCESSFULLY": "Recipe created successfully", "RECIPE_FETCHED_SUCCESSFULLY": "Recipe retrieved successfully", "INVALID_REFERENCE": "Invalid reference", "ERROR_FETCHING_RECIPE": "Error fetching recipe", "ERROR_FETCHING_RECIPES": "Error fetching recipes", "ERROR_FETCHING_RECIPE_HISTORY": "Error fetching recipe history", "ERROR_UPDATING_RECIPE": "Error updating recipe", "ERROR_DUPLICATING_RECIPE": "Error duplicating recipe", "ERROR_UPDATING_IMPRESSION": "Error updating recipe impression", "ERROR_PUBLISHING_RECIPE": "Error publishing recipe", "DUPLICATE_ENTRY": "Duplicate entry found", "RECIPE_UPDATED_SUCCESSFULLY": "Recipe updated successfully", "RECIPES_FETCHED_SUCCESSFULLY": "Recipes retrieved successfully", "PUBLIC_RECIPES_FETCHED_SUCCESSFULLY": "Public recipes retrieved successfully", "PRIVATE_RECIPES_FETCHED_SUCCESSFULLY": "Private recipes retrieved successfully", "RECIPE_DELETED_SUCCESSFULLY": "Recipe deleted successfully", "RECIPE_DUPLICATED_SUCCESSFULLY": "Recipe duplicated successfully", "RECIPE_PUBLISHED_SUCCESSFULLY": "Recipe published successfully", "RECIPE_UNPUBLISHED_SUCCESSFULLY": "Recipe unpublished successfully", "RECIPE_MADE_PUBLIC_SUCCESSFULLY": "<PERSON><PERSON><PERSON> made public successfully", "RECIPE_MADE_PRIVATE_SUCCESSFULLY": "Recipe made private successfully", "RECIPE_BOOKMARKED_SUCCESSFULLY": "Recipe bookmarked successfully", "RECIPE_UNBOOKMARKED_SUCCESSFULLY": "Recipe removed from bookmarks successfully", "RECIPE_IMPRESSION_UPDATED_SUCCESSFULLY": "Recipe impression updated successfully", "RECIPE_HISTORY_FETCHED_SUCCESSFULLY": "Recipe history retrieved successfully", "RECIPE_EXPORTED_SUCCESSFULLY": "Recipe exported successfully", "RECIPES_EXPORTED_SUCCESSFULLY": "Recipes exported successfully", "ERROR_DELETING_RECIPE": "Error deleting recipe", "ERROR_BOOKMARKING_RECIPE": "Error bookmarking recipe", "ERROR_MAKING_RECIPE_PUBLIC": "Error making recipe public", "ERROR_MAKING_RECIPE_PRIVATE": "Error making recipe private", "ERROR_EXPORTING_RECIPE": "Error exporting recipe", "ERROR_EXPORTING_RECIPES": "Error exporting recipes", "RECIPE_NOT_FOUND": "Recipe not found", "RECIPE_ALREADY_EXISTS": "Recipe already exists", "RECIPE_TITLE_REQUIRED": "Recipe title is required", "RECIPE_TITLE_TOO_LONG": "Recipe title must not exceed 100 characters", "RECIPE_DESCRIPTION_TOO_LONG": "Recipe description is too long", "RECIPE_PREPARATION_TIME_INVALID": "Recipe preparation time must be a valid number", "RECIPE_COOK_TIME_INVALID": "Recipe cook time must be a valid number", "RECIPE_STATUS_INVALID": "Recipe status is invalid", "RECIPE_YIELD_INVALID": "Recipe yield must be a valid number", "RECIPE_YIELD_UNIT_INVALID": "Recipe yield unit is invalid", "RECIPE_PORTIONS_INVALID": "Recipe portions must be a valid number", "RECIPE_PORTION_SIZE_INVALID": "Recipe portion size must be a valid number", "RECIPE_SERVING_METHOD_INVALID": "Recipe serving method is invalid", "RECIPE_SERVE_IN_INVALID": "Recipe serve in option is invalid", "RECIPE_INGREDIENTS_REQUIRED": "Recipe must have at least one ingredient", "RECIPE_STEPS_REQUIRED": "<PERSON><PERSON><PERSON> must have at least one step", "RECIPE_CATEGORIES_INVALID": "Recipe categories are invalid", "RECIPE_ATTRIBUTES_INVALID": "Recipe attributes are invalid", "RECIPE_FILES_TOO_MANY": "Too many files uploaded (maximum 10 allowed)", "RECIPE_IMAGE_INVALID": "Recipe image format is invalid", "RECIPE_STEP_IMAGES_TOO_MANY": "Too many step images uploaded", "RECIPE_PERMISSION_DENIED": "You don't have permission to access this recipe", "RECIPE_ALREADY_PUBLISHED": "Recipe is already published", "RECIPE_ALREADY_UNPUBLISHED": "Recipe is already unpublished", "RECIPE_ALREADY_PUBLIC": "Recipe is already public", "RECIPE_ALREADY_PRIVATE": "Recipe is already private", "RECIPE_ALREADY_BOOKMARKED": "Recipe is already bookmarked", "RECIPE_NOT_BOOKMARKED": "Recipe is not bookmarked", "RECIPE_CANNOT_DELETE_PUBLISHED": "Cannot delete published recipe", "RECIPE_CANNOT_EDIT_PUBLISHED": "Cannot edit published recipe", "VALIDATION_ERROR": "Validation error occurred", "DATABASE_ERROR": "Database error occurred", "DATABASE_CONNECTION_ERROR": "Database connection error", "FILE_UPLOAD_ERROR": "File upload error", "FILE_SIZE_TOO_LARGE": "File size is too large", "FILE_TYPE_NOT_ALLOWED": "File type is not allowed", "INVALID_FILE_FORMAT": "Invalid file format", "MISSING_REQUIRED_FIELDS": "Missing required fields", "INVALID_DATA_FORMAT": "Invalid data format", "OPERATION_NOT_ALLOWED": "Operation not allowed", "RESOURCE_NOT_FOUND": "Resource not found", "ACCESS_DENIED": "Access denied", "UNAUTHORIZED_ACCESS": "Unauthorized access", "SESSION_EXPIRED": "Session expired", "RATE_LIMIT_EXCEEDED": "Rate limit exceeded", "SERVICE_UNAVAILABLE": "Service temporarily unavailable", "INTERNAL_SERVER_ERROR": "Internal server error", "BAD_REQUEST": "Bad request", "FORBIDDEN": "Forbidden", "NOT_FOUND": "Not found", "METHOD_NOT_ALLOWED": "Method not allowed", "CONFLICT": "Conflict occurred", "UNPROCESSABLE_ENTITY": "Unprocessable entity", "TOO_MANY_REQUESTS": "Too many requests", "RECIPE_MEASURES_FETCHED_SUCCESSFULLY": "Recipe measures retrieved successfully", "FOOD_ATTRIBUTES_FETCHED_SUCCESSFULLY": "Food attributes retrieved successfully", "CATEGORIES_FETCHED_SUCCESSFULLY": "Categories retrieved successfully", "RECIPE_STEPS_FETCHED_SUCCESSFULLY": "Recipe steps retrieved successfully", "RECIPE_INGREDIENTS_FETCHED_SUCCESSFULLY": "Recipe ingredients retrieved successfully", "RECIPE_RESOURCES_FETCHED_SUCCESSFULLY": "Recipe resources retrieved successfully", "RECIPE_CATEGORIES_FETCHED_SUCCESSFULLY": "Recipe categories retrieved successfully", "RECIPE_ATTRIBUTES_FETCHED_SUCCESSFULLY": "Recipe attributes retrieved successfully", "RECIPE_NUTRITION_FETCHED_SUCCESSFULLY": "Recipe nutrition information retrieved successfully", "RECIPE_ALLERGENS_FETCHED_SUCCESSFULLY": "Recipe allergen information retrieved successfully", "RECIPE_DIETARY_FETCHED_SUCCESSFULLY": "Recipe dietary information retrieved successfully", "RECIPE_CUISINE_FETCHED_SUCCESSFULLY": "Recipe cuisine information retrieved successfully", "RECIPE_HACCP_FETCHED_SUCCESSFULLY": "Recipe HACCP information retrieved successfully", "RECIPE_SEARCH_COMPLETED": "Recipe search completed successfully", "RECIPE_FILTER_APPLIED": "Recipe filters applied successfully", "RECIPE_SORT_APPLIED": "Recipe sorting applied successfully", "RECIPE_PAGINATION_APPLIED": "Recipe pagination applied successfully", "RECIPE_EXPORT_STARTED": "Recipe export started", "RECIPE_EXPORT_COMPLETED": "Recipe export completed successfully", "RECIPE_IMPORT_STARTED": "Recipe import started", "RECIPE_IMPORT_COMPLETED": "Recipe import completed successfully", "RECIPE_VALIDATION_PASSED": "Recipe validation passed", "RECIPE_VALIDATION_FAILED": "Recipe validation failed", "RECIPE_SAVE_DRAFT": "<PERSON><PERSON><PERSON> saved as draft", "RECIPE_AUTO_SAVED": "Recipe auto-saved successfully", "RECIPE_CHANGES_SAVED": "Recipe changes saved successfully", "RECIPE_CHANGES_DISCARDED": "Recipe changes discarded", "RECIPE_BACKUP_CREATED": "Recipe backup created successfully", "RECIPE_BACKUP_RESTORED": "Recipe backup restored successfully", "RECIPE_VERSION_CREATED": "Recipe version created successfully", "RECIPE_VERSION_RESTORED": "Recipe version restored successfully", "RECIPE_SHARED_SUCCESSFULLY": "<PERSON><PERSON><PERSON> shared successfully", "RECIPE_UNSHARED_SUCCESSFULLY": "<PERSON><PERSON><PERSON> unshared successfully", "RECIPE_COPIED_TO_CLIPBOARD": "Recipe copied to clipboard", "RECIPE_LINK_GENERATED": "Recipe link generated successfully", "RECIPE_QR_CODE_GENERATED": "Recipe QR code generated successfully", "RECIPE_PRINT_READY": "Recipe prepared for printing", "RECIPE_PDF_GENERATED": "Recipe PDF generated successfully", "RECIPE_EMAIL_SENT": "Recipe email sent successfully", "RECIPE_NOTIFICATION_SENT": "Recipe notification sent successfully", "PERMISSION_DENIED": "Permission denied", "RECIPE_ALREADY_ARCHIVED": "Recipe is already archived", "RECIPE_ARCHIVED_SUCCESSFULLY": "Recipe archived successfully and all assignments/bookmarks removed", "ERROR_ARCHIVING_RECIPE": "Error archiving recipe", "RECIPE_HAS_ACTIVE_ASSIGNMENTS": "Cannot delete recipe with active user assignments. Please archive the recipe first or remove all assignments.", "SETTINGS_FETCHED_SUCCESSFULLY": "Settings fetched successfully", "SETTING_FETCHED_SUCCESSFULLY": "Setting fetched successfully", "SETTINGS_UPDATED_SUCCESSFULLY": "Settings updated successfully", "SETTING_UPDATED_SUCCESSFULLY": "Setting updated successfully", "SETTING_NOT_FOUND": "Setting not found", "INVALID_SETTINGS_DATA": "Invalid settings data", "SETTING_VALUE_REQUIRED": "Setting value is required", "INVALID_SETTING_KEY": "Invalid setting key", "DEFAULT_SETTINGS_INITIALIZED": "Default settings initialized successfully", "SETTING_DEFINITIONS_FETCHED": "Setting definitions fetched successfully", "SETTINGS_RESET_SUCCESSFULLY": "Settings reset to defaults successfully", "ERROR_FETCHING_SETTINGS": "Error fetching settings", "ERROR_FETCHING_SETTING": "Error fetching setting", "ERROR_UPDATING_SETTINGS": "Error updating settings", "ERROR_UPDATING_SETTING": "Error updating setting", "ERROR_INITIALIZING_SETTINGS": "Error initializing settings", "ERROR_FETCHING_DEFINITIONS": "Error fetching setting definitions", "ERROR_RESETTING_SETTINGS": "Error resetting settings", "DASHBOARD_DATA_FETCHED_SUCCESSFULLY": "Dashboard data fetched successfully", "PUBLIC_ANALYTICS_FETCHED_SUCCESSFULLY": "Public analytics fetched successfully", "DASHBOARD_WIDGETS_FETCHED_SUCCESSFULLY": "Dashboard widgets fetched successfully", "DASHBOARD_DATA_EXPORTED_SUCCESSFULLY": "Dashboard data exported successfully", "ERROR_FETCHING_DASHBOARD_DATA": "Error fetching dashboard data", "ERROR_FETCHING_PUBLIC_ANALYTICS": "Error fetching public analytics", "ERROR_FETCHING_DASHBOARD_WIDGETS": "Error fetching dashboard widgets", "ERROR_EXPORTING_DASHBOARD_DATA": "Error exporting dashboard data", "EVENT_TRACKED_SUCCESSFULLY": "Event tracked successfully", "ANALYTICS_FETCHED_SUCCESSFULLY": "Analytics data fetched successfully", "CTA_ANALYTICS_FETCHED_SUCCESSFULLY": "CTA analytics fetched successfully", "CONTACT_ANALYTICS_FETCHED_SUCCESSFULLY": "Contact analytics fetched successfully", "ANALYTICS_SUMMARY_FETCHED_SUCCESSFULLY": "Analytics summary fetched successfully", "ANALYTICS_DATA_DELETED_SUCCESSFULLY": "Analytics data deleted successfully", "EVENT_TYPE_AND_ENTITY_TYPE_REQUIRED": "Event type and entity type are required", "INVALID_EVENT_TYPE": "Invalid event type", "INVALID_ENTITY_TYPE": "Invalid entity type", "DELETE_CRITERIA_REQUIRED": "Delete criteria required", "ERROR_TRACKING_EVENT": "Error tracking event", "ERROR_FETCHING_ANALYTICS": "Error fetching analytics", "ERROR_FETCHING_CTA_ANALYTICS": "Error fetching CTA analytics", "ERROR_FETCHING_CONTACT_ANALYTICS": "Error fetching contact analytics", "ERROR_FETCHING_ANALYTICS_SUMMARY": "Error fetching analytics summary", "ERROR_DELETING_ANALYTICS_DATA": "Error deleting analytics data", "SUCCESS_DATA_CREATED": "Data create successfully", "PDF_EXPORT_SINGLE_RECIPE_ONLY": "PDF export is only available for single recipe", "RECIPE_IMPRESSION_UPDATED": "Recipe impression updated successfully", "ERROR_UPDATING_BOOKMARK": "Error updating bookmark", "RECIPE_VISIBILITY_UPDATED_SUCCESSFULLY": "Recipe visibility updated successfully", "ERROR_UPDATING_RECIPE_VISIBILITY": "Error updating recipe visibility", "CANNOT_UPDATE_SYSTEM_DEFAULT": "Cannot update system default", "CANNOT_DELETE_SYSTEM_DEFAULT": "Cannot delete system default", "CTA_CLICK_TRACKED_SUCCESSFULLY": "CTA click tracked successfully", "ERROR_TRACKING_CTA_CLICK": "Error tracking CTA click", "CONTACT_FORM_SUBMITTED_SUCCESSFULLY": "Contact form submitted successfully", "ERROR_SUBMITTING_CONTACT_FORM": "Error submitting contact form", "CONTACT_SUBMISSION_NOT_FOUND": "Contact submission not found", "CONTACT_SUBMISSION_DELETED_SUCCESSFULLY": "Contact submission deleted successfully", "ERROR_DELETING_CONTACT_SUBMISSION": "Error deleting contact submission", "DATA_NOT_FOUND": "Data not found", "SUCCESS_DATA_UPDATED": "Data updated successfully", "INVALID_REQUEST_DATA": "Invalid request data", "SUCCESS_DATA_RETRIEVED": "Data retrieved successfully", "RECIPE_VIEW_ANALYTICS_FETCHED_SUCCESSFULLY": "Recipe view analytics fetched successfully", "RECIPE_COMPLEXITY_LEVEL_INVALID": "Recipe complexity level is invalid", "RECIPE_COMPLEXITY_LEVEL_REQUIRED": "Recipe complexity level is required", "ASSIGNED_USERS_FETCHED_SUCCESSFULLY": "Assigned users retrieved successfully", "RECIPES_FILTERED_BY_COMPLEXITY_SUCCESSFULLY": "Recipes filtered by complexity level successfully", "RECIPE_BOOKMARKED": "Recipe bookmarked successfully", "RECIPE_BOOKMARK_REMOVED": "Recipe bookmark removed successfully", "RECIPE_HISTORY_NOT_FOUND": "Recipe history not found", "RECIPE_SLUG_ALREADY_EXISTS": "A recipe with this title already exists. Please try again."}