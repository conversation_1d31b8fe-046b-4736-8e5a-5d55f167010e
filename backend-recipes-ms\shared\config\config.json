{"development": {"use_env_variable": false, "PORT": 8028, "API_BASE_URL": "http://localhost:8028/uploads", "WEB_BASE_URL": "http://localhost:3000", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://localhost:8080/auth/", "KEYCLOAK_REALM_NAME": "orga", "KEYCLOAK_SECRET_KEY": "Tn59hpqfeR6Mg3DODmnp0wKI01ymHLUM", "KEYCLOAK_BASE_URL": "http://localhost:8080/auth/admin/realms/", "RABBITMQ_URL": "amqp://admin:jnext@123@localhost:5673", "CRON_RENEWAL_TIME": "*/10 * * * *", "JWT_SECRET_KEY": "jwt_token_secret", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "API_UPLOAD_URL": "http://localhost:9023/uploads", "JWT_EXIPIRATION_TIME": "1h", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}, "staging": {"use_env_variable": false, "PORT": 9027, "API_BASE_URL": "https://staging.namastevillage.theeasyaccess.com/backend-api/v1/public/user/get-file?location=", "WEB_BASE_URL": "https://staging.namastevillage.theeasyaccess.com", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://keycloak:8080/auth/", "KEYCLOAK_REALM_NAME": "staging_orga", "KEYCLOAK_SECRET_KEY": "IVmrr3Jq3dMGFGsi4YBImbA25KGkr263", "KEYCLOAK_BASE_URL": "http://keycloak:8080/auth/admin/realms/", "RABBITMQ_URL": "amqp://admin:jnext@123@rabbitmq:5672", "CRON_RENEWAL_TIME": "*/10 * * * *", "JWT_SECRET_KEY": "jwt_token_staging_secret", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "API_UPLOAD_URL": "https://staging.namastevillage.theeasyaccess.com:3443/backend-api/uploads", "JWT_EXIPIRATION_TIME": "1h", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}, "production": {"use_env_variable": false, "PORT": 9025, "API_BASE_URL": "", "WEB_BASE_URL": "https://portal.microffice.co.uk", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://keycloak:8080/auth/", "KEYCLOAK_REALM_NAME": "staging_orga", "KEYCLOAK_SECRET_KEY": "IVmrr3Jq3dMGFGsi4YBImbA25KGkr263", "KEYCLOAK_BASE_URL": "http://keycloak:8080/auth/admin/realms/", "RABBITMQ_URL": "amqp://admin:JnextMO2025@rabbitmq:5672", "CRON_RENEWAL_TIME": "*/10 * * * *", "JWT_SECRET_KEY": "jwt_token_staging_secret", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "API_UPLOAD_URL": "https://portal.microffice.co.uk/backend-api/uploads", "JWT_EXIPIRATION_TIME": "1h", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}}