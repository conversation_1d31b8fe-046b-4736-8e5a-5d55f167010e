/**
 * Simple test script to verify the enhanced recipe history API functionality
 * This script tests the new filter parameter and user information display
 */

const axios = require('axios');

// Configuration - Update these values for your environment
const BASE_URL = 'http://localhost:3000/api/v1/private/recipes';
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN_HERE'; // Replace with actual token
const RECIPE_ID = 1; // Replace with actual recipe ID

// Test cases
const testCases = [
  {
    name: 'Test 1: Get all history (no filter)',
    url: `${BASE_URL}/history/${RECIPE_ID}`,
    expectedBehavior: 'Should return all history items'
  },
  {
    name: 'Test 2: Get activity history only',
    url: `${BASE_URL}/history/${RECIPE_ID}?filter=activity`,
    expectedBehavior: 'Should return only activity items (bookmarks, creation, etc.)'
  },
  {
    name: 'Test 3: Get modification history only',
    url: `${BASE_URL}/history/${RECIPE_ID}?filter=history`,
    expectedBehavior: 'Should return only modification items (updates, ingredient changes, etc.)'
  },
  {
    name: 'Test 4: Invalid filter value',
    url: `${BASE_URL}/history/${RECIPE_ID}?filter=invalid`,
    expectedBehavior: 'Should return all history items (ignores invalid filter)'
  },
  {
    name: 'Test 5: Combined with pagination',
    url: `${BASE_URL}/history/${RECIPE_ID}?filter=activity&page=1&limit=5`,
    expectedBehavior: 'Should return paginated activity items'
  }
];

// Activity actions that should be returned with filter=activity
const activityActions = [
  'bookmark_added',
  'bookmark_removed',
  'created',
  'published',
  'archived',
  'restored',
  'deleted'
];

// History actions that should be returned with filter=history
const historyActions = [
  'updated',
  'ingredient_added',
  'ingredient_removed',
  'ingredient_updated',
  'step_added',
  'step_removed',
  'step_updated',
  'category_added',
  'category_removed',
  'attribute_added',
  'attribute_removed',
  'resource_added',
  'resource_removed'
];

async function runTest(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📍 URL: ${testCase.url}`);
  console.log(`📋 Expected: ${testCase.expectedBehavior}`);
  
  try {
    const response = await axios.get(testCase.url, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = response.data;
    
    if (data.status) {
      console.log(`✅ Success: ${data.message}`);
      console.log(`📊 Count: ${data.count} items`);
      
      if (data.data && data.data.length > 0) {
        // Check user information display
        const sampleItem = data.data[0];
        console.log(`👤 Sample user info: "${sampleItem.user}"`);
        
        if (sampleItem.user && sampleItem.user !== 'Unknown User' && !sampleItem.user.includes('user@')) {
          console.log(`✅ User information looks good (shows actual name)`);
        } else {
          console.log(`⚠️  User information might need improvement`);
        }
        
        // Check filtering
        if (testCase.url.includes('filter=activity')) {
          const invalidActions = data.data.filter(item => !activityActions.includes(item.action));
          if (invalidActions.length === 0) {
            console.log(`✅ Activity filter working correctly`);
          } else {
            console.log(`❌ Activity filter failed - found non-activity actions:`, invalidActions.map(i => i.action));
          }
        } else if (testCase.url.includes('filter=history')) {
          const invalidActions = data.data.filter(item => !historyActions.includes(item.action));
          if (invalidActions.length === 0) {
            console.log(`✅ History filter working correctly`);
          } else {
            console.log(`❌ History filter failed - found non-history actions:`, invalidActions.map(i => i.action));
          }
        }
        
        // Show sample actions
        const actions = [...new Set(data.data.map(item => item.action))];
        console.log(`🎯 Actions found: ${actions.join(', ')}`);
      }
    } else {
      console.log(`❌ Failed: ${data.message}`);
    }
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ HTTP Error ${error.response.status}: ${error.response.data?.message || error.message}`);
    } else {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

async function runAllTests() {
  console.log('🚀 Starting Recipe History API Filter Tests');
  console.log('=' .repeat(50));
  
  // Check configuration
  if (AUTH_TOKEN === 'YOUR_AUTH_TOKEN_HERE') {
    console.log('❌ Please update AUTH_TOKEN in the test script');
    return;
  }
  
  for (const testCase of testCases) {
    await runTest(testCase);
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 Tests completed');
  console.log('\n📝 Manual verification checklist:');
  console.log('   ✓ User names show actual names instead of "user"');
  console.log('   ✓ Activity filter returns only activity-related actions');
  console.log('   ✓ History filter returns only modification-related actions');
  console.log('   ✓ No filter returns all actions (backward compatibility)');
  console.log('   ✓ Invalid filter values are ignored gracefully');
  console.log('   ✓ Pagination works with filters');
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testCases };
