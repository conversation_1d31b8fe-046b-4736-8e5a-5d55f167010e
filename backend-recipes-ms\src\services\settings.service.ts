import Settings, { SettingType, SettingCategory } from "../models/Settings";

interface SettingDefinition {
  key: string;
  defaultValue: any;
  type: SettingType;
  category: SettingCategory;
  description: string;
  isSystem: boolean;
}

// RECIPE SETTINGS - EXACTLY MATCHING UI SCREENSHOTS
const DEFAULT_SETTINGS: SettingDefinition[] = [
  // Private Recipe Visibility Settings
  {
    key: "recipe.highlight_changes",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.RECIPE,
    description:
      "Enable this to show highlighted changes to the assigned team member when viewing the recipe",
    isSystem: false,
  },

  // Public Recipe Settings
  {
    key: "recipe.public_store_enabled",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.RECIPE,
    description:
      "Enable to make public recipe features available. Turning this off hides all public recipe options",
    isSystem: false,
  },

  // Public Recipe Call-To-Action Settings
  {
    key: "recipe.cta_contact_form",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Show a basic contact form (Name, Email, Phone, Message)",
    isSystem: false,
  },
  {
    key: "recipe.cta_contact_info",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Display a predefined contact block (Phone, Email, Link)",
    isSystem: false,
  },
  {
    key: "recipe.cta_custom_link",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Show a custom CTA with text and an external link",
    isSystem: false,
  },
  {
    key: "recipe.cta_none",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Show nothing",
    isSystem: false,
  },

  // Contact Info Fields (for when contact info is selected)
  {
    key: "recipe.contact_info_name",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Contact name",
    isSystem: false,
  },
  {
    key: "recipe.contact_info_phone",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Phone number",
    isSystem: false,
  },
  {
    key: "recipe.contact_info_email",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Email address",
    isSystem: false,
  },
  {
    key: "recipe.contact_info_link",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Link URL",
    isSystem: false,
  },

  // Custom CTA Fields (for when custom CTA is selected)
  {
    key: "recipe.custom_cta_text",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Custom CTA button text",
    isSystem: false,
  },
  {
    key: "recipe.custom_cta_link",
    defaultValue: "",
    type: SettingType.STRING,
    category: SettingCategory.PUBLIC,
    description: "Custom CTA link URL",
    isSystem: false,
  },

  // Recipe Details to Display Publicly (exactly as shown in UI screenshots)
  {
    key: "recipe.display_category",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Category",
    isSystem: false,
  },
  {
    key: "recipe.display_ingredients",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Ingredients",
    isSystem: false,
  },
  {
    key: "recipe.display_nutritional_information",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Nutritional Information",
    isSystem: false,
  },
  {
    key: "recipe.display_allergen_information",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Allergen Information",
    isSystem: false,
  },
  {
    key: "recipe.display_preparation_steps",
    defaultValue: true,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Preparation Steps",
    isSystem: false,
  },
  {
    key: "recipe.display_total_time",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Total Time",
    isSystem: false,
  },
  {
    key: "recipe.display_yield_portioning",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Yield & Portioning",
    isSystem: false,
  },
  {
    key: "recipe.display_cost",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Cost",
    isSystem: false,
  },
  {
    key: "recipe.display_dietary_suitability",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Dietary Suitability",
    isSystem: false,
  },
  {
    key: "recipe.display_cuisine_type",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Cuisine Type",
    isSystem: false,
  },
  {
    key: "recipe.display_media",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Media",
    isSystem: false,
  },
  {
    key: "recipe.display_links",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Links",
    isSystem: false,
  },
  {
    key: "recipe.display_scale",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Scale",
    isSystem: false,
  },
  {
    key: "recipe.display_serve_in",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Serve In",
    isSystem: false,
  },
  {
    key: "recipe.display_garnish",
    defaultValue: false,
    type: SettingType.BOOLEAN,
    category: SettingCategory.PUBLIC,
    description: "Garnish",
    isSystem: false,
  },
];

class SettingsService {
  /**
   * Get setting value with fallback to default
   */
  async getSetting(key: string, organizationId?: string): Promise<any> {
    const setting = await Settings.findOne({
      where: {
        setting_key: key,
        organization_id: organizationId || null,
      },
    });

    if (setting) {
      return this.parseSettingValue(setting);
    }

    // Return default value if setting doesn't exist
    const defaultSetting = DEFAULT_SETTINGS.find((s) => s.key === key);
    return defaultSetting ? defaultSetting.defaultValue : null;
  }

  /**
   * Get all settings for a category
   */
  async getSettingsByCategory(
    category: SettingCategory,
    organizationId?: string
  ): Promise<Record<string, any>> {
    const settings = await Settings.findAll({
      where: {
        setting_category: category,
        organization_id: organizationId || null,
      },
    });

    const result: Record<string, any> = {};

    // Add existing settings
    settings.forEach((setting: Settings) => {
      result[setting.setting_key] = this.parseSettingValue(setting);
    });

    // Add missing default settings
    DEFAULT_SETTINGS.filter((def) => def.category === category).forEach(
      (def) => {
        if (!(def.key in result)) {
          result[def.key] = def.defaultValue;
        }
      }
    );

    return result;
  }

  /**
   * Update or create setting
   */
  async updateSetting(
    key: string,
    value: any,
    organizationId?: string,
    userId?: number
  ): Promise<Settings> {
    const defaultSetting = DEFAULT_SETTINGS.find((s) => s.key === key);
    if (!defaultSetting) {
      throw new Error(`Unknown setting key: ${key}`);
    }

    const serializedValue = this.serializeSettingValue(
      value,
      defaultSetting.type
    );

    const [setting] = await Settings.upsert({
      setting_key: key,
      setting_value: serializedValue,
      setting_type: defaultSetting.type,
      setting_category: defaultSetting.category,
      setting_description: defaultSetting.description,
      is_system_setting: defaultSetting.isSystem,
      organization_id: organizationId || null,
      created_by: userId || 1,
      updated_by: userId || 1,
    });

    return setting;
  }

  /**
   * Update multiple settings at once
   */
  async updateSettings(
    settings: Record<string, any>,
    organizationId?: string,
    userId?: number
  ): Promise<void> {
    const promises = Object.entries(settings).map(([key, value]) =>
      this.updateSetting(key, value, organizationId, userId)
    );

    await Promise.all(promises);
  }

  /**
   * Initialize default settings for an organization
   */
  async initializeDefaultSettings(
    organizationId: string,
    userId: number
  ): Promise<void> {
    const existingSettings = await Settings.findAll({
      where: { organization_id: organizationId },
      attributes: ["setting_key"],
    });

    const existingKeys = existingSettings.map((s: Settings) => s.setting_key);
    const missingSettings = DEFAULT_SETTINGS.filter(
      (def) => !existingKeys.includes(def.key)
    );

    if (missingSettings.length > 0) {
      const settingsToCreate = missingSettings.map((def) => ({
        setting_key: def.key,
        setting_value: this.serializeSettingValue(def.defaultValue, def.type),
        setting_type: def.type,
        setting_category: def.category,
        setting_description: def.description,
        is_system_setting: def.isSystem,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await Settings.bulkCreate(settingsToCreate);
    }
  }

  /**
   * Parse setting value based on type
   */
  private parseSettingValue(setting: Settings): any {
    switch (setting.setting_type) {
      case SettingType.BOOLEAN:
        return setting.getBooleanValue();
      case SettingType.NUMBER:
        return setting.getNumberValue();
      case SettingType.JSON:
        return setting.getJsonValue();
      case SettingType.ARRAY:
        return setting.getArrayValue();
      default:
        return setting.getStringValue();
    }
  }

  /**
   * Serialize setting value for storage
   */
  private serializeSettingValue(value: any, type: SettingType): string {
    switch (type) {
      case SettingType.JSON:
      case SettingType.ARRAY:
        return JSON.stringify(value);
      default:
        return String(value);
    }
  }

  /**
   * Get setting definitions (for UI generation)
   */
  getSettingDefinitions(): SettingDefinition[] {
    return DEFAULT_SETTINGS;
  }

  /**
   * Get all settings for an organization (common function for public APIs)
   * Returns both recipe and public settings combined
   */
  async getSettingsByOrganizationId(organizationId: string): Promise<Record<string, any>> {
    try {
      // Get both recipe and public settings for the organization
      const [recipeSettings, publicSettings] = await Promise.all([
        this.getSettingsByCategory(SettingCategory.RECIPE, organizationId),
        this.getSettingsByCategory(SettingCategory.PUBLIC, organizationId)
      ]);

      // Combine both settings into a single object
      return {
        ...recipeSettings,
        ...publicSettings
      };
    } catch (error) {
      console.error("Error in getSettingsByOrganizationId:", error);
      throw error;
    }
  }

  /**
   * Get structured settings for an organization (for public APIs)
   * Returns settings in the same format as getRecipeConfiguration API
   */
  async getStructuredSettingsByOrganizationId(organizationId: string): Promise<Record<string, any>> {
    try {
      // Get both recipe and public settings for the organization
      const [recipeSettings, publicSettings] = await Promise.all([
        this.getSettingsByCategory(SettingCategory.RECIPE, organizationId),
        this.getSettingsByCategory(SettingCategory.PUBLIC, organizationId)
      ]);

      // Structure the settings in the same format as getRecipeConfiguration
      const structuredSettings = {
        // Private Recipe Visibility Settings
        privateRecipeVisibilitySettings: {
          highlightChanges: recipeSettings["recipe.highlight_changes"] ?? false,
        },

        // Public Recipe Settings
        publicRecipeSettings: {
          publicStoreAccess:
            recipeSettings["recipe.public_store_enabled"] || false,
        },

        // Public Recipe Call-To-Action (CTA)
        publicRecipeCallToAction: {
          contactForm: publicSettings["recipe.cta_contact_form"] ?? true,
          contactInfo: {
            enabled: publicSettings["recipe.cta_contact_info"] || false,
            name: publicSettings["recipe.contact_info_name"] || "",
            phone: publicSettings["recipe.contact_info_phone"] || "",
            email: publicSettings["recipe.contact_info_email"] || "",
            link: publicSettings["recipe.contact_info_link"] || "",
          },
          customCtaLink: {
            enabled: publicSettings["recipe.cta_custom_link"] || false,
            text: publicSettings["recipe.custom_cta_text"] || "",
            link: publicSettings["recipe.custom_cta_link"] || "",
          },
          none: publicSettings["recipe.cta_none"] || false,
        },

        // Recipe Details to Display Publicly
        recipeDetailsToDisplayPublicly: {
          category: publicSettings["recipe.display_category"] ?? true,
          ingredients: publicSettings["recipe.display_ingredients"] ?? true,
          nutritionalInformation:
            publicSettings["recipe.display_nutritional_information"] ?? true,
          allergenInformation:
            publicSettings["recipe.display_allergen_information"] ?? true,
          preparationSteps:
            publicSettings["recipe.display_preparation_steps"] ?? true,
          totalTime: publicSettings["recipe.display_total_time"] ?? false,
          yieldPortioning:
            publicSettings["recipe.display_yield_portioning"] ?? false,
          cost: publicSettings["recipe.display_cost"] ?? false,
          dietarySuitability:
            publicSettings["recipe.display_dietary_suitability"] ?? false,
          cuisineType: publicSettings["recipe.display_cuisine_type"] ?? false,
          media: publicSettings["recipe.display_media"] ?? false,
          links: publicSettings["recipe.display_links"] ?? false,
          scale: publicSettings["recipe.display_scale"] ?? false,
          serveIn: publicSettings["recipe.display_serve_in"] ?? false,
          garnish: publicSettings["recipe.display_garnish"] ?? false,
        },
      };

      return structuredSettings;
    } catch (error) {
      console.error("Error in getStructuredSettingsByOrganizationId:", error);
      throw error;
    }
  }
}

export default new SettingsService();
